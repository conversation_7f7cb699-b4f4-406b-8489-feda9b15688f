# -*- coding: utf-8 -*-
from flask_bootstrap import <PERSON><PERSON><PERSON>
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import <PERSON><PERSON><PERSON><PERSON>ger
# from flask_wtf import CSRFProtect

from concurrent.futures import <PERSON><PERSON>oolExecutor, ThreadPoolExecutor


bootstrap = Bootstrap()
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()

G_WebSocket_Dict = {}  # 用来保存创建的websocket句柄
G_multiprocess_Pool = ProcessPoolExecutor(max_workers=5)

# csrf = CSRFProtect()


# @login_manager.user_loader
# def load_user(user_id):
#     from voice_presentation_sys.models import User
#     admin = User.query.get(int(user_id))
#     return admin


login_manager.login_view = 'auth.login'
login_manager.login_message = u'请先登录'
login_manager.login_message_category = 'warning'

