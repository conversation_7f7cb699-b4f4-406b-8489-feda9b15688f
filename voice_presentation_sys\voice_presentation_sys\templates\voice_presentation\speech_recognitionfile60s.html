{% extends 'basefile.html' %}

{% block content %}
    <div class="container-fluid main-container">
        <div class="row">
            <div class="col-sm-2 nav-frame">
                <ul class="nav nav-pills" id="myTab" role="tablist">
                    <li class="nav-item nav-li-container">
                        <a class="nav-link btn btn-outline-primary tab-top active" id="asr" href="#lang" data-toggle="tab" role="tab">中文识别</a>
                    </li>
                    <li class="nav-item nav-li-container">
                        <a class="nav-link btn btn-outline-primary tab" id="mn-asr" href="#lang" data-toggle="tab" role="tab">英文识别</a>
                    </li>
                    <li class="nav-item nav-li-container">
                        <a class="nav-link btn btn-outline-primary tab" id="sex-asr" href="#lang" data-toggle="tab" role="tab">粤语识别</a>
                    </li>
                </ul>
            </div>
            <div class="col content-area-container">
                <textarea class="border rounded voice-content-area" id="voice-content" maxlength="200" readonly></textarea>
                <div class="bottom-container">
                    <!--autoplay表明在音频准备就绪后直接进行播放-->
                    <audio class="hidden" id="audio" autoplay src="#"></audio>
                    <button hidden id="random-play" type="button" class="btn btn-light" data-lang="putonghua">
                        随机播放
                        <span class="fi-audio"></span>
                    </button>
                    <button id="record-button" type="button" class="btn btn-primary float-right" data-type="asr">
                        开始录音
                        <span class="fi-microphone"></span>
                    </button>
                    <button hidden id="recording-button" type="button" class="btn btn-primary float-right">
                        停止录音
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="sr-only"></span>
                        </div>
                    </button>
                    <button id="upload-wav-button" type="button" class="btn btn-light upload-btn" data-toggle="modal"
                            data-target="#confirm-upload">
                        上传文件(文件是必须小于60秒的单声道16K采样线性PCM的WAV语音)
                        <span class="fi-cloud-upload"></span>
                    </button>
                </div>
                <!-- 文件上传模态框 -->
                <div class="modal fade" id="confirm-upload" tabindex="-1" role="dialog" aria-labelledby="文件上传" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <!--此处附带next值，redirect_back-->
                                <form id="upload-form" enctype="multipart/form-data">
                                    <input id="file"  type="file"  name="wav_file" required accept=".wav">
                                    <input id="submit-file" type="button" aria-hidden="true" data-dismiss="modal"
                                           aria-label="Close" class="btn btn-secondary" value="提交">
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
     
{% endblock content %}

{% block scripts %}
    {{ super() }}
    <script type="text/javascript" src="{{ url_for('static', filename='js/speech_recognition_js_file.js') }}"></script>
{% endblock scripts %}
   