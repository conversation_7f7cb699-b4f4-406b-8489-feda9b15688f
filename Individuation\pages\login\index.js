// index.js
// 获取应用实例
const app = getApp()
var common = require("./../../dist/js/common.js")
Page({
    data: {
        motto: '开始使用',
        userInfo: app.globalData.userInfo,
        hasUserInfo: false,
        canIUse: wx.canIUse('button.open-type.getUserInfo'),
        canIUseGetUserProfile: false,
        canIUseOpenData: wx.canIUse('open-data.type.userAvatarUrl') && wx.canIUse('open-data.type.userNickName'), // 如需尝试获取用户信息可改为false
        getPhoneNumberError: ""
    },
    // 获取手机号码
    getPhoneNumber(e) {
        // wx.showToast({
        //     title: "get phone number 异常",
        //     duration: 3000,
        //     icon: "error",
        //     success: function () {
        //     }
        // })
        if (e.detail.errMsg == "getPhoneNumber:fail user deny") {
            wx.showToast({
                title: '请允许获取手机号，否则功能不可用',
                icon: 'none'
            })
            return
        }
        if (e.detail.errMsg == "getPhoneNumber:fail no permission") {
            wx.showToast({
                title: '没有获取用户手机号的权限',
                icon: 'none'
            })
            return
        }

        wx.showLoading({
            title: '登录中',
        })

        common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/util/wechat/get_phone_number", {
            code: e.detail.code
        }, {
            token: app.globalData.userInfo.token
        }, "GET").then(res => {
            if (res.data.state == "error") {
                wx.showToast({
                    title: '登录异常',
                    duration: 2000,
                    icon: "error",
                    success: function () {
                    }
                })
                wx.hideLoading({
                    success: (res) => {},
                })
            }
            app.globalData.userInfo.mobile = res.data.mobile
            app.globalData.userInfo.token = res.data.token
            wx.setStorageSync('token', res.data.token)
            wx.hideLoading({
                success: (res) => {},
            })
            wx.showToast({
                title: '登录成功',
                duration: 2000,
                icon: "success",
                success: function () {
                    setTimeout(function () {
                        wx.navigateBack()  //返回跳转登录页面前 的页面
                    }, 1500) //延迟时间
                }
            })
        }).catch(err => {
            this.setData({
                getPhoneNumberError: err.errMsg
            })
            wx.hideLoading({
                success: (res) => {},
            })
        })
    },
    // 事件处理函数
    bindGetMobile() {
        common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/auth/login", {"end_userid": "13506950386"}, {}, "POST").then(res => {
            console.log(res.data)
            // wx.showToast({
            //     title: '登录成功',
            //     duration: 2000,
            //     icon: "success",
            //     success: function () {
            //         setTimeout(function () {
            //             wx.navigateBack()  //返回跳转登录页面前 的页面
            //         }, 1500) //延迟时间
            //     }
            // })
        }).catch(err => {
            this.setData({
                getPhoneNumberError: err.errMsg
            })
        })

        // wx.showLoading({
        //     title: '登录中',
        // })
        // // 获取手机号码 并登录获取token 存入缓存
        // app.globalData.userInfo.mobile = "wxdemo123456"
        // app.globalData.userInfo.token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTY1MjA3Njc0MCwianRpIjoiODMyMzNiMzgtNzBiMS00MzAyLWFiMWUtNDQ1ZTk2NzI3MzliIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6Ind4ZGVtbzEyMyIsIm5iZiI6MTY1MjA3Njc0MCwiZXhwIjoxNjU0NjY4NzQwfQ.TMPnb0HEGn_oXuVhif9XiGmUBmyUOjTTxnTO-F1JuGo"
        // wx.setStorageSync('token', "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTY1MjA3Njc0MCwianRpIjoiODMyMzNiMzgtNzBiMS00MzAyLWFiMWUtNDQ1ZTk2NzI3MzliIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6Ind4ZGVtbzEyMyIsIm5iZiI6MTY1MjA3Njc0MCwiZXhwIjoxNjU0NjY4NzQwfQ.TMPnb0HEGn_oXuVhif9XiGmUBmyUOjTTxnTO-F1JuGo")
        // wx.hideLoading({
        //     success: (res) => {},
        // })
        // wx.showToast({
        //     title: '登录成功',
        //     duration: 1500,
        //     icon: "success",
        //     success: function () {
        //         setTimeout(function () {
        //             wx.redirectTo({
        //                 url: '../index/index'
        //             })
        //         }, 1500) //延迟时间
        //     }
        // })
    },
    onShow() {
        if (wx.canIUse('button.open-type.getPhoneNumber')){
            // wx.showToast({
            //     title: wx.canIUse('button.open-type.getPhoneNumber').toString(),
            //     duration: 2000
            //   })
            return
        }
        else {
            wx.showToast({
                title: "当前微信版本不支持当前手机号码登录，请更新至最新版",
                duration: 2000
              })
        }
    },
    getUserProfile(e) {
        // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
        wx.getUserProfile({
            desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
            success: (res) => {
                console.log(res)
                this.setData({
                    userInfo: res.userInfo,
                    hasUserInfo: true
                })
            }
        })
    },
})