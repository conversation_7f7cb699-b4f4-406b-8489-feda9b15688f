import{compareDay,getMonthEndDay}from"../../util";import*as config from"../../config";Component({data:{days:[]},properties:{minDate:{type:Object,optionalTypes:[String,null]},maxDate:{type:Object,optionalTypes:[String,null]},formatter:{type:null,observer:"setDays"},date:{type:null,observer:"setDays"},currentDate:{type:Array,optionalTypes:[null],observer(){this.setDays()}},type:{type:String,observer:"setDays"},showMonthTitle:Boolean,color:{type:String,value:""}},methods:{onClick(t){const{item:e}=t.currentTarget.dataset;"disabled"!==e.type&&"empty"!==e.type&&this.triggerEvent("clickDay",e)},debounce(t){let e;return()=>{let a=this,r=arguments;e&&clearTimeout(e),e=setTimeout((function(){t.apply(a,r)}),300)}},setDays(){this.debounce(this.setDay)()},setDay(){let t=[];const e=new Date(this.data.date),a=e.getFullYear(),r=e.getMonth(),n=new Date(a,r,1).getDay(),o=getMonthEndDay(a,r+1);for(let e=1;e<=o;e++){const n=new Date(a,r,e).getTime(),o=this.getDayType(n);let s={date:n,type:o,text:e,bottomInfo:this.getBottomInfo(o),topInfo:""};this.data.formatter&&(s=this.data.formatter(s)),t.push(s)}for(let e=0;e<n;e++)t.unshift({type:"empty"});this.setData({days:t})},isDateInCurrent(t){const{currentDate:e}=this.data;return e.some(e=>0===compareDay(e,t))},getMultipleDayType(t){const{currentDate:e}=this.data;return Array.isArray(e)&&this.isDateInCurrent(t)?"selected":""},getRangeDayType(t){const{currentDate:e}=this.data;if(!Array.isArray(e))return;const[a,r]=e;if(!a)return;const n=compareDay(t,a);if(!r)return 0===n?"start":"";const o=compareDay(t,r);return 0===n?"start":0===o?"end":n>0&&o<0?"middle":void 0},getDayType(t){const{type:e,minDate:a,maxDate:r,currentDate:n}=this.data;return compareDay(t,a)<0||compareDay(t,r)>0?"disabled":e===config.TYPE_SINGLE?0===compareDay(t,n)?"selected":"":e===config.TYPE_MULTIPLE?this.getMultipleDayType(t):e===config.TYPE_RANGE?this.getRangeDayType(t):void 0},getBottomInfo(t){return this.data.type===config.TYPE_RANGE?"start"===t?"开始":"end"===t?"结束":"":""}}});