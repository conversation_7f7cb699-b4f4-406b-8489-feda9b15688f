import validator from"../behaviors/validator";Component({externalClasses:["l-class","l-title-class","l-avatar-class","l-row-class"],behaviors:[validator],properties:{loading:{type:Boolean,value:!0},title:{type:Boolean,value:!0},paragraph:{type:Boolean,value:!0},active:{type:Boolean,value:!0},avatar:Boolean,titleWidth:String,avatarSize:String,avatarShape:{type:String,value:"circle",options:["circle","square"]},rowsWidth:{type:Array,optionalTypes:[Array,String],value:"60%"},rowsHeight:{type:Array,optionalTypes:[Array,String],value:"34rpx"},rows:Number},observers:{"rows,rowsWidth,rowsHeight":function(t,a,e){this._getResult(t,a,"rowsW","100%"),this._getResult(t,e,"rowsH","34rpx"),this._toRows(t)}},data:{},methods:{_arrRepeat(t,a){const e=[];for(let r=0;r<a-1;r++)e.push(t);return e},_getResult(t,a,e,r){if(Array.isArray(a))this.data[e]=a;else{const s=this._arrRepeat(r,t);s.push(a),this.data[e]=s}},_toRows(t){let a=[];for(let e=0;e<t;e++)a.push({width:this.data.rowsW[e],height:this.data.rowsH[e]});this.setData({r:a})}}});