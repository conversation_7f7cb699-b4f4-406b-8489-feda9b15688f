<view class="container">
    <view class="subcontainer-1" wx:if="{{!switchPage}}">
        <view class="container-top">
            <l-icon name="success" size="50" color="#fff" style="margin-right: 20rpx;" />
            <text style="font-size: 40rpx;">声音已完成录制</text>
        </view>
        <l-form name="student" l-form-container-class="l-form-container-class" bind:linsubmit="onModelFormSubmit">
            <l-form-item label="模型名称:" name="modelName" align-items="center" rules="{{modelNameRules}}">
                <l-input id="modelName" value="{{model.name}}" hide-label show-row="{{false}}" />
            </l-form-item>

            <l-form-item label="模型性别:" name="modelGender" rules="{{modelGnderRules}}">
                <l-radio-group current="{{model.gender}}" id="modelGender" placement="row" none-checked="{{false}}">
                    <l-radio key="0" l-class="l-form-radio-class">
                        男
                    </l-radio>
                    <l-radio key="1" l-class="l-form-radio-class">
                        女
                    </l-radio>
                </l-radio-group>
            </l-form-item>

            <view slot="submit" style="margin-left: 80rpx;bottom: 5%;">
                <l-button width="500" bg-color="#007fff">开始定制</l-button>
            </view>
            <l-toast></l-toast>
        </l-form>
    </view>
    <view class="subcontainer-2" wx:else>
        <text style="margin-top: 40%;font-size: 40rpx;">音色定制中……</text>
        <text style="margin-top: 10rpx">定制时间10分钟左右，请耐心等待，定制成功后会以短信的信息通知您。</text>
        <l-button width="500" bg-color="#007fff" bind:lintap="onRedirectIndex" style="position: absolute;bottom: 10%;">返回首页</l-button>
    </view>
</view>