<view class="container">
  <l-textarea placeholder="请输入文本内容" bind:lininput="bindTextAreaBlur" value="{{textcontent}}" 
    l-class="text-content" maxlength="1000" indicator="{{true}}" />

  <view class="middle-container" style="height: 200rpx;">
    <!-- <l-tabs l-line-class="tabs-line-choose"
    active-color="#007fff" bind:linchange="onTabChange"> -->

      <!-- <l-tabpanel tab="通用场景" key="one" slot="one">
        <scroll-view enable-flex scroll-x class="langs-container">
          <block wx:for="{{general}}" wx:key="index">
            <language2 name="{{item.nickName}}" bindtap="onChangeActive" data-name="{{item.nickName}}" data-spkId="{{item.spkId}}" activeName="{{currentActiveName}}" />
          </block>
        </scroll-view>
      </l-tabpanel>
      -->

      <!-- <l-tabpanel tab="方言场景" key="two" slot="two">
        <scroll-view enable-flex scroll-x class="langs-container">
          <block wx:for="{{localism}}" wx:key="index">
            <language2 name="{{item.nickName}}" bindtap="onChangeActive" data-name="{{item.nickName}}" data-spkId="{{item.spkId}}" activeName="{{currentActiveName}}" />
          </block>
        </scroll-view>
      </l-tabpanel>  -->

      <!-- <l-tabpanel tab="多语种" key="three" slot="three" style="height: 200rpx;"> -->
        <scroll-view enable-flex scroll-x class="langs-container">
          <block wx:for="{{multilanguageSpks}}" wx:key="index">
            <language2 name="{{item.nickName}}" bindtap="onChangeActive" data-name="{{item.nickName}}" data-spkId="{{item.spkId}}" activeName="{{currentActiveName}}" />
          </block>
        </scroll-view>
        <view class="lang-blocks-container">
          <block wx:for="{{multilanguageIds}}" wx:key="index">
            <lang-block langName="{{item.langName}}" langId="{{item.langId}}" bindtap="onChangeActiveMultilang" data-name="{{item.langName}}" data-langId="{{item.langId}}" activeLangId="{{currentMultilangId}}" style="margin-right: 60rpx;" />
          </block>
        </view>
      <!-- </l-tabpanel> -->

    <!-- </l-tabs> -->
    <view class="hr-container">
      <view class="horizon"></view>
    </view>
  </view>

  <view class="all-img">
    <!-- 未播放状态 -->
    <image src="../../images/stop.png" class="play-img" bindtap='playStart' wx-if="{{audioStatus=='stop'}}" />
    <!-- 播放状态 -->
    <image src="../../images/play.png" class="play-img" bindtap='playPaused' wx-if="{{audioStatus=='playing'}}" />
    <!-- 暂停状态未播放 -->
    <image src="../../images/stop.png" class="play-img" bindtap='playGoOn' wx-if="{{audioStatus=='paused'}}" />
  </view>

</view>