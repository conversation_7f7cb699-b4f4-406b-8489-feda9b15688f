<!-- 多语言语音合成 -->
<view class="container">
  <!-- 文本输入区域 -->
  <view class="text-container">
    <textarea value="{{textcontent}}" placeholder="请输入要进行语音合成的文本内容"
              bindinput="bindTextAreaBlur" maxlength="1000" class="text-input"></textarea>
  </view>

  <!-- 音色选择区域 -->
  <view class="voice-section">
    <text class="section-title">选择音色</text>
    <scroll-view scroll-x class="voice-list">
      <block wx:for="{{multilanguageSpks}}" wx:key="index">
        <view class="voice-item {{currentActiveName == item.nickName ? 'voice-active' : ''}}"
              bindtap="onChangeActive" data-name="{{item.nickName}}" data-spkId="{{item.spkId}}">
          <text class="voice-name">{{item.nickName}}</text>
        </view>
      </block>
    </scroll-view>
  </view>

  <!-- 语言选择区域 -->
  <view class="language-section">
    <text class="section-title">选择语言</text>
    <view class="language-list">
      <block wx:for="{{multilanguageIds}}" wx:key="index">
        <view class="language-item {{currentMultilangId == item.langId ? 'language-active' : ''}}"
              bindtap="onChangeActiveMultilang" data-name="{{item.langName}}" data-langId="{{item.langId}}">
          <text class="language-name">{{item.langName}}</text>
        </view>
      </block>
    </view>
  </view>

  <!-- 播放控制区域 -->
  <view class="play-section">
    <!-- 未播放状态 -->
    <image src="../../images/stop.png" class="play-button" bindtap='playStart' wx:if="{{audioStatus=='stop'}}" />
    <!-- 播放状态 -->
    <image src="../../images/play.png" class="play-button" bindtap='playPaused' wx:if="{{audioStatus=='playing'}}" />
    <!-- 暂停状态 -->
    <image src="../../images/stop.png" class="play-button" bindtap='playGoOn' wx:if="{{audioStatus=='paused'}}" />
    <text class="play-tip">点击播放按钮开始语音合成</text>
  </view>
</view>