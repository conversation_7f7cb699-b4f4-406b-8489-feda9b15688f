export function getMonthEndDay(t,e){return new Date(t,e,0).getDate()}export function isTimeTemp(t){return/^\d+(\.\d+)?$/.test(t)}export function getDate(t){return isTimeTemp(t)&&(t=parseInt(t)),t=new Date(t)}export function getDayByOffset(t,e){return(t=new Date(t)).setDate(t.getDate()+e),t}export function compareMonth(t,e){t instanceof Date||(t=getDate(t)),e instanceof Date||(e=getDate(e));const n=t.getFullYear(),a=e.getFullYear(),o=t.getMonth(),r=e.getMonth();return n===a?o===r?0:o>r?1:-1:n>a?1:-1}export function compareDay(t,e){t instanceof Date||(t=getDate(t)),e instanceof Date||(e=getDate(e));const n=compareMonth(t,e);if(0===n){const n=t.getDate(),a=e.getDate();return n===a?0:n>a?1:-1}return n}export function calcDateNum(t){const e=new Date(t[0]).getTime();return(new Date(t[1]).getTime()-e)/864e5+1}export function copyDates(t){return Array.isArray(t)?t.map(t=>null===t?t:new Date(t)):new Date(t)}export function getTime(t){return Array.isArray(t)?t.map(t=>t instanceof Date?t.getTime():t):t instanceof Date?t.getTime():t}export function formatMonthTitle(t){return t instanceof Date||(t=new Date(t)),`${t.getFullYear()}年${t.getMonth()+1}月`}export function getMonths(t,e){const n=[],a=new Date(t);a.setDate(1);do{n.push(a.getTime()),a.setMonth(a.getMonth()+1)}while(1!==compareMonth(a,e));return n}