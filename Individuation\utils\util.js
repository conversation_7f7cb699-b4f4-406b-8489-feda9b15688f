var MD5 = require("./md5.js")

async function bdts(from, to, content) {
  var q = content
  //注册获得appid和密匙
  var appid = '20220125001067391'
  var key = 'vKFv1vOKfNU0p1pSi8Hz'
  var salt = (new Date).getTime();
  var string = appid + q + salt + key
  var sign = MD5.MD5(string) //MD5是一个写好的加密函数，复制黏贴就好
  //传入的参数名应该是严格要求的，比如"q"改成"value"是不允许的
  var resp = () => {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://fanyi-api.baidu.com/api/trans/vip/translate',
        data: {
          q,
          from,
          to,
          appid,
          salt,
          sign
        },
        success: res => {
          if (res.statusCode == 200) {
            resolve(res.data.trans_result[0].dst)
          } else {
            wx.showToast({
              icon: "none",
              title: '翻译服务异常，请使用其它语种'
            })
          }
        },
        fail: res => {
          wx.showToast({
            icon: "none",
            title: '翻译服务请求异常，请使用其它语种'
          })
          reject(res) // reject通常返回Error实例
        }
      })
    })
  }
  return await resp()
}

// 去除字符串末尾所有非中英文数字
function dealSymbol(val) {
  let re = new RegExp('^[\u4e00-\u9fa5a-zA-Z0-9]+$', 'i');
  while (!re.test(val.substr(-1))) {
    console.log("val: ", val)
    val = val.substr(0, val.length - 1);
  }
  return val;
}

// 英文标点转中文 标点前后加空格
function puncEn2Zh(s) {
  s = s.replace(/:/g, '：'); // 正则  g是全局匹配
  s = s.replace(/[.]/g, '。'); // 这个也加个[]才正常点
  s = s.replace(/"/g, '“');
  s = s.replace(/"/g, '”');
  s = s.replace(/,/g, '，');
  s = s.replace(/[?]/g, '？'); // /?/g 不知道为啥不行
  s = s.replace(/,/g, '、');
  s = s.replace(/;/g, '；');
  s = s.replace(/'/g, '‘');
  s = s.replace(/'/g, '’');
  var new_s = ""
  //   const puncsReg = /[：。“”，？、；‘’]/g
  for (var i = 0, len = s.length; i < len; i++) {
    new_s = new_s + s[i]
  }
  return new_s
}

function sleep(numberMillis) {
  var now = new Date();
  var exitTime = now.getTime() + numberMillis;
  while (true) {
    now = new Date();
    if (now.getTime() > exitTime)
      return;
  }
}

function formatTime(date) {
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()

  var hour = date.getHours()
  var minute = date.getMinutes()
  var second = date.getSeconds();


  return [year, month, day].map(formatNumber).join('/') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

function formatNumber(n) {
  n = n.toString()
  return n[1] ? n : '0' + n
}

const request = (url, options) => {
  console.log("url, options", url, options)
  return wx.request({
    url: url,
    method: options.method,
    data: options.data,
    header: {
      'content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    success(res) {
      return options.cb_fn && options.cb_fn(res.data) //回调函数
    },
    fail(err) {
      console.log("err: ", err)
    }
  })
}


const post = (url, jsonData, cb_fn) => {
  return request(url, {
    method: 'POST',
    data: jsonData,
    cb_fn: cb_fn
  })
}

function toUtf8(str) {
  var out, i, len, c;
  out = "";
  len = str.length;
  for (i = 0; i < len; i++) {
    c = str.charCodeAt(i);
    if ((c >= 0x0001) && (c <= 0x007F)) {
      out += str.charAt(i);
    } else if (c > 0x07FF) {
      out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
      out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
      out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
    } else {
      out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
      out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
    }
  }
  return out;
}

/**
 * 使用循环的方式判断一个元素是否存在于一个数组中
 * @param {Object} arr 数组
 * @param {Object} value 元素值
 */
function isInArray(arr, value) {
  for (var i = 0; i < arr.length; i++) {
    if (value === arr[i]) {
      return true;
    }
  }
  return false;
}

// TTS API配置 - 统一管理所有TTS相关的API端点
const TTS_CONFIG = {
  // 基础TTS服务配置 (用于闽南语等非普通话)
  BASE_TTS: {
    URL: "https://tctts.talentedsoft.com/dotctts",
    GET_WAV_URL: "https://tctts.talentedsoft.com/tts/",
    USERID: "tc_wx_yun7",
    TOKEN: "15705951797"
  },

  // 普通话专用TTS服务配置 - 使用Flask项目的proxy_tts接口
  MANDARIN_TTS: {
    URL: "https://tc.talentedsoft.com:58120/ajax/proxy_tts",
    USERID: "yhy",
    TOKEN: "1234"
  },

  // 个性化TTS服务配置 - 与Flask项目保持一致
  INDIVIDUATION_TTS: {
    // 个性化TTS训练URL (对应Flask的TRAIN_TTS_URL)
    TRAIN_URL: "http://125.77.202.194:9398/doxvector",
    // 个性化TTS合成URL (对应Flask的GEN_TTS_URL)
    SYNTHESIS_URL: "http://125.77.202.194:9398/dotctts0",
    // 获取TTS音频URL (对应Flask的GET_TTS_URL)
    GET_WAV_URL: "http://125.77.202.194:3350/dotcgettts"
  },



  // 默认参数配置 - 与Flask项目保持一致
  DEFAULT_PARAMS: {
    spkid: 0, // 默认说话人ID - 小樱 (与Flask项目一致)
    lanid: 0, // 默认语言ID (0=普通话, 3=闽南语)
    volume: 1.0 // 默认音量
  }
}

// 统一的TTS请求函数 - 根据语言ID选择不同的服务
function ttsRequest(content, options = {}) {
  const spkid = options.spkid || TTS_CONFIG.DEFAULT_PARAMS.spkid
  const lanid = options.lanid || TTS_CONFIG.DEFAULT_PARAMS.lanid

  // 根据语言ID选择不同的TTS服务，与Flask项目逻辑一致
  if (lanid == 0) {
    // 普通话使用新版中英说话人模型
    return mandarinTtsRequest(content, spkid)
  } else {
    // 其他语言使用旧版模型
    return otherLanguageTtsRequest(content, spkid, lanid)
  }
}

// 普通话TTS请求函数 - 使用Flask项目的proxy_tts接口
function mandarinTtsRequest(content, spkid) {
  const config = TTS_CONFIG.MANDARIN_TTS
  const params = {
    spkid: spkid,
    lanid: 0,
    content: content
  }

  return new Promise((resolve, reject) => {
    // 使用GET请求下载音频文件，与Flask的proxy_tts接口一致
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')

    wx.downloadFile({
      url: `${config.URL}?${queryString}`,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve({
            audioUrl: res.tempFilePath
          })
        } else {
          reject(new Error('普通话语音合成异常'))
        }
      },
      fail: (err) => {
        reject(new Error('普通话语音合成请求失败'))
      }
    })
  })
}

// 其他语言TTS请求函数 - 使用旧版模型
function otherLanguageTtsRequest(content, spkid, lanid) {
  const config = TTS_CONFIG.BASE_TTS
  const params = {
    userid: config.USERID,
    token: config.TOKEN,
    content: content,
    spkid: spkid,
    lanid: lanid
  }

  // 特殊音量处理
  if (params.spkid == 3) {
    params.volume = 2.5
  }

  return new Promise((resolve, reject) => {
    wx.request({
      url: config.URL,
      method: "POST",
      data: params,
      header: {
        'content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
      },
      success: (res) => {
        if (res.data.errCode !== "0") {
          reject(new Error('语音合成异常'))
          return
        }
        if (res.data.wavfile) {
          const audioUrl = config.GET_WAV_URL + res.data.wavfile
          resolve({
            audioUrl,
            wavfile: res.data.wavfile
          })
        } else {
          reject(new Error('未获取到音频文件'))
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

// 个性化TTS合成请求函数 - 与Flask项目API保持一致
function individuationTtsRequest(content, subuser) {
  const config = TTS_CONFIG.INDIVIDUATION_TTS

  // 使用与Flask项目相同的参数格式
  const params = {
    userid: TTS_CONFIG.BASE_TTS.USERID, // 使用基础TTS的userid
    token: TTS_CONFIG.BASE_TTS.TOKEN, // 使用基础TTS的token
    content: content,
    subuser: subuser
  }

  return new Promise((resolve, reject) => {
    // 构建完整的URL，将参数拼接到URL中
    const fullUrl = `${config.SYNTHESIS_URL}?userid=${params.userid}&token=${params.token}&content=${encodeURIComponent(params.content)}&subuser=${params.subuser}`

    // 使用GET请求下载音频文件，与Flask的gen_tts API一致
    wx.downloadFile({
      url: fullUrl,
      header: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve({
            audioUrl: res.tempFilePath
          })
        } else {
          reject(new Error('个性化语音合成异常'))
        }
      },
      fail: (err) => {
        reject(new Error('个性化语音合成请求失败'))
      }
    })
  })
}

// 个性化TTS训练请求函数 - 与Flask项目API保持一致
function individuationTrainRequest(audioFilePath, subuser) {
  const config = TTS_CONFIG.INDIVIDUATION_TTS

  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: config.TRAIN_URL,
      filePath: audioFilePath,
      name: 'file',
      formData: {
        userid: TTS_CONFIG.BASE_TTS.USERID,
        token: TTS_CONFIG.BASE_TTS.TOKEN,
        subuser: subuser
      },
      header: {
        'Content-Type': 'multipart/form-data'
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          if (data.state === "success") {
            resolve(data)
          } else {
            reject(new Error(data.msg || '训练请求失败'))
          }
        } catch (e) {
          reject(new Error('训练响应解析失败'))
        }
      },
      fail: (err) => {
        reject(new Error('训练请求失败'))
      }
    })
  })
}



// 统一的错误处理函数
function handleTtsError(error, showToast = true) {
  console.error('TTS Error:', error)

  if (showToast) {
    wx.showToast({
      title: error.message || '语音合成异常',
      icon: 'none',
      duration: 2000
    })
  }

  // 隐藏加载提示
  wx.hideLoading()
}

// 音频播放管理器
class AudioManager {
  constructor() {
    this.audioContext = null
    this.isPlaying = false
    this.currentAudioUrl = null
  }

  // 初始化音频上下文
  init() {
    if (!this.audioContext) {
      this.audioContext = wx.createInnerAudioContext()
      this.audioContext.useWebAudioImplement = true
      this.registerEvents()
    }
    return this.audioContext
  }

  // 注册音频事件
  registerEvents() {
    if (!this.audioContext) return

    this.audioContext.onPlay(() => {
      this.isPlaying = true
    })

    this.audioContext.onPause(() => {
      this.isPlaying = false
    })

    this.audioContext.onStop(() => {
      this.isPlaying = false
    })

    this.audioContext.onEnded(() => {
      this.isPlaying = false
    })

    this.audioContext.onError((err) => {
      console.error('Audio Error:', err)
      this.isPlaying = false
      handleTtsError(new Error('音频播放失败'))
    })
  }

  // 播放音频
  play(audioUrl) {
    if (!this.audioContext) {
      this.init()
    }

    this.audioContext.src = audioUrl
    this.audioContext.play()
    this.currentAudioUrl = audioUrl

    // 设置全局播放选项
    wx.setInnerAudioOption({
      obeyMuteSwitch: false,
      mixWithOther: false,
    })
  }

  // 暂停播放
  pause() {
    if (this.audioContext && this.isPlaying) {
      this.audioContext.pause()
    }
  }

  // 停止播放
  stop() {
    if (this.audioContext) {
      this.audioContext.stop()
    }
  }

  // 销毁音频上下文
  destroy() {
    if (this.audioContext) {
      this.audioContext.destroy()
      this.audioContext = null
    }
  }
}

module.exports = {
  dealSymbol: dealSymbol,
  formatTime: formatTime,
  toUtf8: toUtf8,
  post: post,
  puncEn2Zh: puncEn2Zh,
  bdts: bdts,
  sleep: sleep,
  isInArray: isInArray,
  TTS_CONFIG: TTS_CONFIG,
  ttsRequest: ttsRequest,
  individuationTtsRequest: individuationTtsRequest,
  individuationTrainRequest: individuationTrainRequest,
  handleTtsError: handleTtsError,
  AudioManager: AudioManager
}