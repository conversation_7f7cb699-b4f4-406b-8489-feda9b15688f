import zIndex from"../behaviors/zIndex";import validator from"../behaviors/validator";import eventUtil from"../core/utils/event-util";import doNothing from"../core/behaviors/doNothingBehavior";Component({behaviors:[zIndex,validator,doNothing],externalClasses:["l-bg-class","l-panel-class","l-class"],properties:{show:{type:Boolean,value:!1},animation:{type:<PERSON>olean,value:!0},transition:{type:<PERSON>olean,value:null},contentAlign:{type:String,value:"center",options:["top","right","left","bottom","center"]},direction:{type:String,value:null,options:["top","right","left","bottom","center"]},locked:{type:Boolean,value:!1}},attached(){this._init()},pageLifetimes:{show(){this._init()}},data:{status:"hide"},observers:{show(t){t&&this.setData({status:"show"})}},methods:{_init(){wx.lin=wx.lin||{},wx.lin.showPopup=t=>{console.warn("wx.lin 方法已废弃，请使用开放函数代替 https://doc.mini.talelin.com/start/open-function.html"),this.linShow(t)},wx.lin.hidePopup=()=>{console.warn("wx.lin 方法已废弃，请使用开放函数代替 https://doc.mini.talelin.com/start/open-function.html"),this.linHide()}},onPopupTap(){!0!==this.data.locked&&this._hidePopup(),eventUtil.emit(this,"lintap")},_hidePopup(){this.data.animation?(this.setData({status:"hide"}),setTimeout(()=>{this.setData({show:!1})},300)):this.setData({show:!1,status:"hide"})},linShow(t){const{zIndex:i=99,animation:o=!0,contentAlign:e="center",locked:n=!1}={...t};this.setData({zIndex:i,animation:o,contentAlign:e,locked:n,show:!0})},linHide(){this._hidePopup()}}});