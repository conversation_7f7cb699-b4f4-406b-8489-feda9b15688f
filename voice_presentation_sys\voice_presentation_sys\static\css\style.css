.bg-img{
    width:100%;
    height:100%;
    position:absolute;
    left:0;
    top:0;
    right: 0;
    bottom: 0;
    z-index:-1;
}

.intro-text {
    text-align: center;
    padding-top: 8rem;
    padding-bottom: 13rem;
    padding-left: -4rem;
}

.intro-lead-in {
    font-size: 2rem;
    line-height: 2.3rem;
    margin-bottom: 3.2rem;
    margin-right: 4rem;
    color: #ffffff;
}

.intro-heading {
    text-transform: uppercase;
    font-weight: 500;
    font-size: 2.3rem;
    line-height: 4rem;
    margin-bottom: 2.3rem;
    margin-right: 16rem;
    color: #ffffff;
}

.intro-container{
    margin-left: 5rem;
}

.logo {
    float: left;
    transform:scale(0.5);
}

.index-btn{
    margin-right: 3.8rem;
    color: #158CA2;
}

.navbar-container {
    padding-right: 4rem;
    padding-left: 4rem;
}
.navbar-font{
    font-size: 15px;
}

.main-container{
    padding-top: 1.8rem;
    padding-bottom: 3rem;
    padding-left: 3rem;
}

.content-area-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: -5rem;
}

.voice-content-area {
    width: 60%;
    height: 20rem;
    font-family: "Microsoft YaHei", sans-serif;
    font-size: 1.5rem;
}

.stream-voice-content-area {
    width: 90%;
    height: 15rem;
    font-family: "Microsoft YaHei", sans-serif;
    font-size: 1rem;
}

.stream-voice-content-area2 {
    width: 90%;
    height: 5rem;
    font-family: "Microsoft YaHei", sans-serif;
    font-size: 1rem;
}

#top-container{
    width: 60%;
    display: flex;
}

.bottom-container{
    width: 60%;
    height: 2.5rem;
    margin-top: 1.5rem;
}

.bottom-container2{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 60%;
    height: 2.5rem;
    margin-top: 1.5rem;
}

.stream-bottom-container{
    width: 90%;
    height: 2.5rem;
    margin-top: 1.5rem;
}

.nav-frame {
    padding-left: 40px;
    padding-right: 25px;
}

.nav-li-container{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tab{
    margin-top: 10px;
    width: 180px;
}

.tab-top{
    margin-top: 3px;
    width: 180px;
}

.tips{
    float: left;
}

.download-btn{
    margin-right: 10px;
    float: right;
}

.upload-btn{
    margin-right: 10px;
    float: right;
}

#toast{
    display: none;
    width: 15rem;
    margin-left: -6rem;
    background-color: #84aedc;
    color: #fff;
    text-align: center;
    border-radius: 5px;
    padding: 1.2rem;
    position: absolute;
    z-index: 1;
    left: 50%;
    bottom: 28%;
}

#toast-close-btn{
    margin-bottom: 0;
    line-height: 1;
    color: #ffffff;
}

.btn{
    font-size: 0.7rem !important;
}

.topic{
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
}

/* speech_synthesis2*/
.voice-content-area2{
    height: 10rem;
    width: 80%;
    font-family: "Microsoft YaHei", sans-serif;
    font-size: 1.5rem;
    margin-top: 1rem;
}

/* id= voice-content-id*/
#voice-content-id{
    height: 3rem;
    width: 80%;
    font-size: 1.2rem;
    font-family: "Microsoft YaHei", sans-serif;
}

.content-area-container2{
    display: flex;
    flex-direction: column;
    align-items: center;
}

.container-fluid-padding{
    padding: 3rem 3rem;
}

.checkbox{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 10rem;
    width: 80%
}

.wave-form{
    width: 28rem;
    height: 14rem;
}
