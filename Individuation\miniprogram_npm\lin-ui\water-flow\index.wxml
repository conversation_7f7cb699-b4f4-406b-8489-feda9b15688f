<view class="l-class water-flow-container" wx:if="{{data.length!==0}}">
  <view class="water-column" style="margin-right:{{columnGap}}">
    <view id="left">
      <block wx:for="{{leftData}}" wx:key="index">
        <l-water-flow-item data-item="{{item}}" catch:tap="onTapItem" data="{{item}}"/>
      </block>
    </view>
  </view>
  <view class="water-column">
    <view id="right">
      <block wx:for="{{rightData}}" wx:key="index">
        <l-water-flow-item data-item="{{item}}" catch:tap="onTapItem" data="{{item}}"/>
      </block>
    </view>
  </view>
</view>
