<!-- 录制普通话，播放闽南语 -->
<view class="container">
    <view class="text-container">
        <textarea value="{{asrResult}}" placeholder="这里将显示录音音频的识别结果" disabled="{{!switchTTS}}" bindtap="onSwitchTTS" bindinput="bindTextAreaInput"></textarea>
    </view>

    <!-- 音色选择器 -->
    <view class="voice-selector" wx:if="{{switchTTS}}">
        <text class="voice-label">选择音色：</text>
        <picker bindchange="onVoiceChange" value="{{selectedVoice}}" range="{{voiceOptions}}" range-key="name">
            <view class="picker-view">
                <text class="selected-voice">{{voiceOptions[selectedVoice].name}}</text>
                <text class="picker-arrow">▼</text>
            </view>
        </picker>
    </view>

    <text class="top-tip">点击输入框可切换为播放闽南语模式，长按播放按钮重置为初始录音状态。</text>
    <image src="../../images/luyin.gif" bindtap='startRecord' class="center-img" wx-if="{{isluyin}}" />
    <view class="all-img" bindlongpress="onSwitchRecord">
        <view hidden="{{switchTTS}}">
            <view class="{{isluyin?'bofanimgtwo on':'bofanimgtwo'}}" bindlongpress ="startRecord" bindtouchend="stopRecord"></view>
        </view>
        <view hidden="{{!switchTTS}}">
            <!-- 未播放状态 -->
            <image src="../../images/stop.png" class="play-img" bindtap='playStart' wx-if="{{audioStatus=='stop'}}" />
            <!-- 播放状态 -->
            <image src="../../images/play.png" class="play-img" bindtap='playPaused' wx-if="{{audioStatus=='playing'}}" />
            <!-- 暂停状态未播放 -->
            <image src="../../images/stop.png" class="play-img" bindtap='playGoOn' wx-if="{{audioStatus=='paused'}}" />
        </view>
  </view>
  <text class="tip">{{tip}}</text>
</view>
