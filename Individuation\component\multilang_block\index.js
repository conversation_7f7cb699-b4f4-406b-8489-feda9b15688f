// component/multilang_block/index.js
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        langName:String,
        langId:String,
        activeLangId:String,
    },

    /**
     * 组件的初始数据
     */
    data: {
        // 组件是否激活标识(被点击)
        active:false
    },

    //数据监听器
    observers: {
        "activeLangId": function(activeLangId){
            if (activeLangId != this.data.langId){
                this.setData({
                    active: false
                })
            }
            else{
                this.setData({
                    active: true
                })
            }
        }
    },

    /**
     * 组件的方法列表
     */
    methods: {
    }
})
