{% extends 'base.html' %}

{% block content %}
    <div class="container-fluid main-container">
        <div class="row">
            <div class="col-sm-2">
                <ul class="nav nav1" role="tablist">
                    <li class="nav-item nav-li-container">
                        <p class="topic">说话人类别</p>
                        <a class="nav-link btn btn-outline-primary tab-top lang-spks-tab active" data-toggle="tab" href="#" role="tab" data-spkid="0">  <!--data-id 指的是说话人spkId-->
                            小樱（温柔知性）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab lang-spks-tab" data-toggle="tab" href="#" role="tab" data-spkid="3">
                            小智（严肃正式）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab lang-spks-tab" data-toggle="tab" href="#" role="tab" data-spkid="5">
                            小雅（端庄优雅）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab lang-spks-tab" data-toggle="tab" href="#" role="tab" data-spkid="6">
                            小童（阳光开朗）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab lang-spks-tab" data-toggle="tab" href="#" role="tab" data-spkid="4">
                            小漫（活泼俏皮）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab lang-spks-tab" data-toggle="tab" href="#" role="tab" data-spkid="1">
                            小天（清亮文雅）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab lang-spks-tab" data-toggle="tab" href="#" role="tab" data-spkid="2">
                            小果（天真可爱）
                        </a>
                    </li>
                </ul>
                <ul class="nav nav2" role="tablist">
                    <li class="nav-item nav-li-container" style="margin-top: 10px">
                        <p class="topic">发音类别</p>
                        <a class="nav-link btn btn-outline-primary tab-top lang-choose-tab active" data-toggle="tab" href="#" role="tab" data-lanid="0">
                            普通话
                        </a>
                        <a class="nav-link btn btn-outline-primary tab lang-choose-tab" data-toggle="tab" href="#" role="tab" data-lanid="3"><!--langid指当前发音的类型-->
                            闽南语（厦门口音）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab lang-choose-tab" data-toggle="tab" href="#" role="tab" data-lanid="1">
                            闽南语（泉州口音）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab-top lang-choose-tab" data-toggle="tab" href="#" role="tab" data-lanid="4">
                            台湾普通话
                        </a>
                        <a class="nav-link btn btn-outline-primary tab-top lang-choose-tab" data-toggle="tab" href="#" role="tab" data-lanid="5">
                            英语（美式发音）
                        </a>
                        <a class="nav-link btn btn-outline-primary tab-top lang-choose-tab" data-toggle="tab" href="#" role="tab" data-lanid="6">
                            英语（英式发音）
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col content-area-container">
                <textarea class="border rounded voice-content-area" id="voice-content" maxlength="1000"></textarea>
                <div class="bottom-container">
                    <div class="tips">
                        <!--autoplay表明在音频准备就绪后直接进行播放-->
                        <audio class="hidden" id="audio" autoplay="autoplay" src="" type="audio/wav"></audio>
                        还可以输入
                        <a id="word-nums" style="color: #fd7e14;">1000</a>
                        字
                    </div>
                    <button id="play-button" type="button" class="btn btn-primary float-right" data-lanid="0" data-spkid="0">  <!--默认langid id-->
                        开始播放
                        <span class="fi-caret-right"></span>
                    </button>
                    <button id="loading-button" type="button" class="btn btn-primary float-right" hidden>
                        正在播放
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="sr-only"></span>
                        </div>
                    </button>
                    <button class="btn download-btn">
                        <a id="wav-download" class="fi-cloud-download" href="#" download=""></a>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div align="center" style="font-size:16px;color:#000"> &nbsp;&nbsp;&nbsp;&nbsp;    联系电话: 0592-5998812 &nbsp;&nbsp;&nbsp;&nbsp;        
     <a href="https://www.talentedsoft.com/" style=" color:#000;">公司网站链接</a> &nbsp;&nbsp;    
    </div>
{% endblock content %}

{% block scripts %}
    {{ super() }}
    <script type="text/javascript" src="{{ url_for('static', filename='js/speech_synthesis.js') }}"></script>
{% endblock scripts %}
