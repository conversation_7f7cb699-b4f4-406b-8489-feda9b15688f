# -*- coding: utf-8 -*-
from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileRequired, FileAllowed
from wtforms import <PERSON><PERSON>ield, SubmitField, BooleanField, PasswordField, IntegerField
from wtforms.validators import DataRequired, Length


class LoginForm(FlaskForm):
    username = StringField('用户名称', validators=[DataRequired(), Length(1, 20)])
    password = PasswordField('密码', validators=[DataRequired(), Length(1, 128)])
    remember = BooleanField('记住我')
    submit = SubmitField('登录')


class UploadWavForm(FlaskForm):
    file = FileField('上传文件', validators=[FileAllowed(['wav'], '文件必须为(.wav)的后缀格式')])
    submit = SubmitField('提交')