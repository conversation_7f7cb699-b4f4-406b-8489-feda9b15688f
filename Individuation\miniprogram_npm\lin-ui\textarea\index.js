import rules from"../behaviors/rules";import eventBus from"../core/utils/event-bus";Component({behaviors:["wx://form-field",rules],externalClasses:["l-class","l-error-text","l-error-text-class","l-inner-class"],properties:{placeholder:{type:String,value:""},value:{type:String,value:""},focus:{type:Boolean,value:!1},maxlength:{type:Number,value:140},indicator:{type:Boolean,value:!0},autoHeight:{type:Boolean,value:!1},disabled:{type:Boolean,value:!1},border:{type:Boolean,value:!0},rules:{type:Object},placeholderStyle:{type:String,value:""},cursorSpacing:{type:Number,value:0}},data:{},attached(){this.initRules()},methods:{handleInputChange(e){const{detail:t={}}=e,{value:a=""}=t;this.setData({value:a}),eventBus.emit("lin-form-change-"+this.id,this.id),this.triggerEvent("lininput",e.detail)},handleInputFocus(e){this.triggerEvent("linfocus",e.detail)},handleInputBlur(e){this.validatorData({[this.data.name]:e.detail.value}),eventBus.emit("lin-form-blur-"+this.id,this.id),this.triggerEvent("linblur",e.detail)},handleInputConfirm(e){this.triggerEvent("linconfirm",e.detail)},getValues(){return this.data.value},reset(){this.data.value=""}}});