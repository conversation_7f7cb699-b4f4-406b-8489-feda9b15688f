// pages/voice_clone.js - 语音克隆页面
const app = getApp()
const util = require("../../utils/util.js")
const { voiceCloneRequest, handleTtsError, AudioManager } = util

Page({
    data: {
        // 文本内容
        textContent: '您好，这里是天聪智能声云语音演示中心，很高兴为您服务！',
        // 录音状态
        isRecording: false,
        // 录音文件路径
        recordFilePath: '',
        // 音频管理器
        audioManager: null,
        // 录音管理器
        recorderManager: null,
        // 是否正在合成
        isSynthesizing: false,
        // 合成结果音频URL
        resultAudioUrl: ''
    },

    onLoad: function (options) {
        // 初始化音频管理器
        const audioManager = new AudioManager()
        audioManager.init()
        
        // 初始化录音管理器
        const recorderManager = wx.getRecorderManager()
        
        this.setData({
            audioManager: audioManager,
            recorderManager: recorderManager
        })
        
        this.initRecorderEvents()
    },

    // 初始化录音事件
    initRecorderEvents: function() {
        const recorderManager = this.data.recorderManager
        
        recorderManager.onStart(() => {
            console.log('录音开始')
            this.setData({
                isRecording: true
            })
        })
        
        recorderManager.onStop((res) => {
            console.log('录音结束', res)
            this.setData({
                isRecording: false,
                recordFilePath: res.tempFilePath
            })
            
            wx.showToast({
                title: '录音完成',
                icon: 'success',
                duration: 1500
            })
        })
        
        recorderManager.onError((err) => {
            console.error('录音错误', err)
            this.setData({
                isRecording: false
            })
            
            wx.showToast({
                title: '录音失败',
                icon: 'error',
                duration: 2000
            })
        })
    },

    // 文本输入监听
    onTextInput: function(e) {
        this.setData({
            textContent: e.detail.value
        })
    },

    // 开始录音
    startRecord: function() {
        if (this.data.isRecording) {
            return
        }
        
        // 检查录音权限
        wx.getSetting({
            success: (res) => {
                if (!res.authSetting['scope.record']) {
                    wx.authorize({
                        scope: 'scope.record',
                        success: () => {
                            this.doStartRecord()
                        },
                        fail: () => {
                            wx.showToast({
                                title: '需要录音权限',
                                icon: 'none',
                                duration: 2000
                            })
                        }
                    })
                } else {
                    this.doStartRecord()
                }
            }
        })
    },

    // 执行录音
    doStartRecord: function() {
        const options = {
            duration: 60000, // 最长录音时间60秒
            sampleRate: 16000, // 采样率
            numberOfChannels: 1, // 录音通道数
            encodeBitRate: 96000, // 编码码率
            format: 'wav' // 音频格式
        }
        
        this.data.recorderManager.start(options)
    },

    // 停止录音
    stopRecord: function() {
        if (!this.data.isRecording) {
            return
        }
        
        this.data.recorderManager.stop()
    },

    // 播放录音
    playRecord: function() {
        if (!this.data.recordFilePath) {
            wx.showToast({
                title: '请先录音',
                icon: 'none',
                duration: 2000
            })
            return
        }
        
        this.data.audioManager.play(this.data.recordFilePath)
    },

    // 开始语音克隆
    startVoiceClone: function() {
        if (!this.data.textContent.trim()) {
            wx.showToast({
                title: '请输入要合成的文本',
                icon: 'none',
                duration: 2000
            })
            return
        }
        
        if (!this.data.recordFilePath) {
            wx.showToast({
                title: '请先录制音频样本',
                icon: 'none',
                duration: 2000
            })
            return
        }
        
        if (this.data.isSynthesizing) {
            return
        }
        
        this.setData({
            isSynthesizing: true
        })
        
        wx.showLoading({
            title: '正在克隆语音...'
        })
        
        voiceCloneRequest(this.data.textContent, this.data.recordFilePath)
            .then(result => {
                wx.hideLoading()
                
                // 创建临时音频文件
                const fs = wx.getFileSystemManager()
                const tempAudioPath = `${wx.env.USER_DATA_PATH}/voice_clone_${Date.now()}.wav`
                
                // 将音频数据写入临时文件
                fs.writeFile({
                    filePath: tempAudioPath,
                    data: result.audioData,
                    encoding: 'binary',
                    success: () => {
                        this.setData({
                            resultAudioUrl: tempAudioPath,
                            isSynthesizing: false
                        })
                        
                        wx.showToast({
                            title: '语音克隆成功',
                            icon: 'success',
                            duration: 2000
                        })
                    },
                    fail: (err) => {
                        console.error('写入音频文件失败', err)
                        this.setData({
                            isSynthesizing: false
                        })
                        
                        wx.showToast({
                            title: '保存音频失败',
                            icon: 'error',
                            duration: 2000
                        })
                    }
                })
            })
            .catch(error => {
                wx.hideLoading()
                this.setData({
                    isSynthesizing: false
                })
                
                handleTtsError(error, true)
            })
    },

    // 播放合成结果
    playResult: function() {
        if (!this.data.resultAudioUrl) {
            wx.showToast({
                title: '请先进行语音克隆',
                icon: 'none',
                duration: 2000
            })
            return
        }
        
        this.data.audioManager.play(this.data.resultAudioUrl)
    },

    // 页面卸载时清理资源
    onUnload: function() {
        if (this.data.audioManager) {
            this.data.audioManager.destroy()
        }
        
        if (this.data.recorderManager) {
            this.data.recorderManager.onStart(() => {})
            this.data.recorderManager.onStop(() => {})
            this.data.recorderManager.onError(() => {})
        }
    }
})
