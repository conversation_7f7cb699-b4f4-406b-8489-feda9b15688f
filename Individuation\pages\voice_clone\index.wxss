/* 语音克隆页面样式 */
page {
  background-color: #f5f5f5;
}

.container {
  padding: 40rpx;
  min-height: 100vh;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 60rpx;
}

.section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

/* 文本输入区域 */
.text-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #fafafa;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 录音区域 */
.record-area {
  text-align: center;
}

.record-tips {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.record-controls {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.record-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50%;
  width: 200rpx;
  height: 200rpx;
  position: relative;
  transition: all 0.3s ease;
}

.record-btn.recording {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  transform: scale(1.1);
}

.record-btn:disabled {
  opacity: 0.5;
}

.record-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.record-text {
  font-size: 24rpx;
}

.play-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 30rpx 40rpx;
  font-size: 26rpx;
}

.play-btn:disabled {
  background: #ccc;
}

/* 克隆按钮 */
.clone-btn {
  width: 100%;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  position: relative;
  overflow: hidden;
}

.clone-btn:disabled {
  opacity: 0.6;
}

.clone-btn.loading {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-content text:first-child {
  margin-right: 20rpx;
  font-size: 36rpx;
}

.icon-loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 结果播放区域 */
.result-area {
  text-align: center;
}

.play-result-btn {
  background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx 60rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-result-btn text:first-child {
  margin-right: 20rpx;
  font-size: 36rpx;
}

/* 帮助说明 */
.help-section {
  background: #e3f2fd;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-top: 40rpx;
}

.help-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 30rpx;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.help-content text {
  font-size: 26rpx;
  color: #424242;
  line-height: 1.4;
  padding-left: 20rpx;
  position: relative;
}

.help-content text::before {
  content: "•";
  color: #1976d2;
  position: absolute;
  left: 0;
}

/* 图标字体 */
.iconfont {
  font-family: "iconfont";
}

.icon-mic::before { content: "🎤"; }
.icon-stop::before { content: "⏹"; }
.icon-play::before { content: "▶"; }
.icon-clone::before { content: "🔄"; }
.icon-loading::before { content: "⏳"; }
