$(function () {
    var recording_click_sign = 0;  //0代表recording_button未被点击，1代表已被点击，每次recording-button点击事件完毕重置
    var recorder

    $('#record-button').on('click', function () {

        // 做一个简单验证
        if ($('#voice-content-id').val() == ""){
            toast(['请先输入userid'], 2000)
            return
        }
        if (recorder == null) {  // 如果recorder实例被清除，那么重新创建实例对象。
            recorder = Recorder({
                type: "wav",
                bitRate: 16,                 // 采样位数，支持 8 或 16，默认是16
                sampleRate: 16000,              // 采样率，支持 11025、16000、22050、24000、44100、48000
                onProcess: function (buffers, powerLevel, bufferDuration, bufferSampleRate) {
                }
            });
        }

        var t = setTimeout(function () {
            console.log("无法录音：权限请求被忽略!");
        }, 8000);

        recorder.open(function () {//打开麦克风授权获得相关资源
            clearTimeout(t);
            recorder.start();//开始录音
            $('#recording-button').attr("hidden", false);
            $('#record-button').attr("hidden", true);

        }, function (msg, isUserNotAllow) {
            clearTimeout(t);
            console.log((isUserNotAllow ? "UserNotAllow，" : "") + "无法录音:" + msg);
        });  // 暂时设置为大延时(才能在手动停止录音后，残留的定时事件不影响之后的录音)
    })

    $('#recording-button').on('click', function () {
        if (recording_click_sign === 1) {  //如果当前事件未结束时 又被触发点击事件 则退出事件
            return toast(['请不要重复点击停止按钮'], 1500);
        }

        recording_click_sign = 1 // (全局参数)第一次开始本事件后，置为1，表示事件已被触发，再次点击将不再触发。(事件结尾重置)

        recorder.stop(function (blob, duration) {
            $('#recording-button').attr("hidden", true);
            $('#record-button').attr("hidden", false);
            let formData = new FormData();

            formData.append('wav_blob', blob);  //这是blob数据
            formData.append("subuser", $('#voice-content-id').val());
            formData.append('file_type', "record_file");  // 实时录音解析 or 上传文件解析 的标识

            $.ajax({
                type: 'POST',
                url: '/ajax/tts2/dotctraintts',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if (data.state === 'Success') {
                        toast(['语音训练成功'], 1500)
                    } else {
                        toast([data.msg], 1500);  //toast弹窗显示错误信息
                    }
                },
                error: function () {
                    toast(['请求异常, 请重试.'], 1500)
                }
            })
        })
        // 销毁录音实例
        recorder.close();
        // 重复点击标识复位
        recording_click_sign = 0;
    })

    $('#submit-file').on('click', function () {
        // 做一个简单验证
        if ($('#voice-content-id').val() == ""){
            toast(['请先输入 subuser 标识'], 2000)
            return
        }

        // 获取表单对象
        let fm = $('#upload-form');
        let formData = new FormData(fm[0]);
        formData.append("subuser", $('#voice-content-id').val());
        formData.append("file_type", "upload_file");  // 因后台使用同一个处理函数，所以此处作为一个标记。

        $.ajax({
            type: 'POST',
            url: '/ajax/tts2/dotctraintts',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                if (data.state == 'Success') {
                    toast(['语音训练成功'], 1500)
                } else {
                    toast([data.msg], 1500);  //toast弹窗显示错误信息
                }
            },
            error: function () {
                toast(['请求异常, 请重试.'], 1500)
            }
        });
    })

    $('#play-button').on('click', function (e) {
        let content = $("input[type='radio']:checked").val()
        let id = $('#voice-content-id')
        let audio_dom = $('#audio')[0];  //audio的dom对象
        let audio_jq = $('#audio');  //audio的jq对象
        let play_button = $('#play-button');
        let load_button = $('#loading-button');

        if (id.val() == ""){
            toast(['请先输入 subuser 标识'], 2000)
            return
        }
        if (content == ""){
            toast(['请先输入要合成的内容'], 2000)
            return
        }

        play_button.attr("disabled", true); //播放按钮不可点击
        audio_jq.attr('src', "/ajax/tts2/dotcgentts?subuser=" + id.val() + "&content=" + content);

        //audio播放开始事件监听
        audio_dom.addEventListener('play', function () {
            play_button.attr("disabled", true); //播放按钮不可点击
            play_button.attr("hidden", true);
            load_button.attr("hidden", false);
        })

        //audio播放结束事件监听
        audio_dom.addEventListener('ended', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);

            play_button.removeAttr('disabled')  // 移除不可点击
        })

        //audio播放异常事件
        audio_dom.addEventListener('ended', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);

            play_button.removeAttr('disabled')  // 移除不可点击
        })

    })

    $('#loading-button').on('click', function (e) {
        let play_button = $('#play-button');
        let load_button = $('#loading-button');
        let audio_dom = $('#audio')[0];  //audio的dom对象
        audio_dom.pause();  // 终止播放
        play_button.attr("hidden", false);
        load_button.attr("hidden", true);
        play_button.removeAttr('disabled')  // 移除不可点击
    })
})