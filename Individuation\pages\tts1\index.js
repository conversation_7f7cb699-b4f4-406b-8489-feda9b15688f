//获取应用实例
const app = getApp()
var common = require("../../utils/util.js")
const {
    ttsRequest,
    handleTtsError
} = common
Page({
    data: {
        // 是否长按录音
        isluyin: false,
        isanzhu: false,
        //语音播放状态
        audioStatus: "stop",
        //语音播放上下文
        innerAudioContext: {},
        //语音录制上下文
        recorderManager: {},
        //语音识别结果
        asrResult: "",
        // 输入框是否禁用
        textareaDisable: true,
        // 是否从录音切换为播放
        switchTTS: false,
        // 提示
        tip: "按住麦克风按钮，然后开始录音",
        // 当前客户端类型
        currentClient: ""
    },

    // 初始化RecorderManager
    initRecorderManager() {
        let _this = this;
        _this.setData({
            recorderManager: wx.getRecorderManager()
        })
        // 录音出错
        _this.data.recorderManager.onError(err => {
            console.log("录音失败: ", err)
            this.setData({
                isluyin: false
            })
            wx.showToast({
                title: "录音失败，请确认开启录音权限，然后重试",
                icon: "error",
                duration: 2000
            })
        })
        // 录音开始
        _this.data.recorderManager.onStart(res => {
            console.log("录音开始")
        })
        // 录音停止
        _this.data.recorderManager.onStop(function (res) { // 录音停止的回调
            // console.log("录音结束: ", res)
            // console.log("合法路径: ", wx.env.USER_DATA_PATH)
            // let manager = wx.getFileSystemManager()
            // manager.saveFile({
            //     tempFilePath: res.tempFilePath, 
            //     filePath: wx.env.USER_DATA_PATH + "/1.wav",
            //     success: res => {
            //         console.log("success: ", res)
            //     },
            //     fail: err => {
            //         console.log("fail: ", err)
            //     }
            // })
            _this.setData({
                isluyin: false
            })
            wx.showLoading({
                title: "加载中"
            })
            var url = "https://tc.talentedsoft.com:58120/ajax/wechat/dotcasr_proxy"
            var formData = {
                "userid": "tc_wx_yun7",
                "token": "wx5678",
                "asr_type": "putonghua" // 普-闽页面应该识别普通话
            }
            // PC端录音无法录制成指定语音格式，通过python代理加一道语音转换作为兼容。
            // 接口加在web版的演示平台服务voice_prezentation_sys
            if (_this.data.currentClient == "windows") {
                formData["asr_type"] = "putonghua" // 修正：应该是普通话识别
                url = "https://tc.talentedsoft.com:58120/ajax/wechat/dotcasr_proxy"
            }
            //1.上传识别，获取识别结果文本
            wx.uploadFile({
                url: url,
                filePath: res.tempFilePath,
                name: 'file',
                formData: formData,
                success: function (res) {
                    console.log("asr1: ", res)
                    if (res.data) {
                        console.log("asr: ", res)
                        var data = JSON.parse(res.data)
                        if (data.errCode == "0") {
                            if (!data.result) {
                                wx.hideLoading({
                                    success: (res) => {},
                                })
                                wx.showToast({
                                    title: "音频不含有效内容，请重新录制",
                                    duration: 2000,
                                    icon: "none"
                                })
                            } else { // 返回正常合法结果
                                _this.setData({
                                    asrResult: data.result
                                })
                                console.log(data)
                                // 2.上传文本合成闽南语
                                let url = "https://tctts.talentedsoft.com/dotctts";
                                let getWavUrl = "https://tctts.talentedsoft.com/tts/"
                                let jsonData = {
                                    "userid": "tc_wx_yun7",
                                    "token": "15705951797",
                                    "content": data.result,
                                    "spkid": 0,
                                    "lanid": 3
                                }
                                let innerAudioContext = _this.data.innerAudioContext;
                                wx.request({
                                    url: url,
                                    method: "POST",
                                    data: jsonData,
                                    header: {
                                        'content-type': 'application/x-www-form-urlencoded;charset=UTF-8'
                                    },
                                    success(res) {
                                        console.log("tts2: ", res)
                                        wx.hideLoading({
                                            success: (res) => {},
                                        })
                                        if (res.data.errCode !== "0") {
                                            wx.showToast({
                                                title: '闽南语语音合成异常',
                                                duration: 2000,
                                                icon: "none"
                                            })
                                            return
                                        } else {
                                            let src = getWavUrl + res.data.wavfile
                                            innerAudioContext.src = src
                                            innerAudioContext.play();
                                        }
                                    },
                                    fail(err) {
                                        console.error('TTS Error:', err)
                                        wx.hideLoading({
                                            success: (res) => {},
                                        })
                                        wx.showToast({
                                            title: '闽南语语音合成异常',
                                            duration: 2000,
                                            icon: "none"
                                        })
                                    }
                                })
                            }
                        } else {
                            wx.hideLoading({
                                success: (res) => {},
                            })
                            wx.showToast({
                                title: "音频异常，未能正常识别，请重新录制",
                                duration: 2000,
                                icon: "none"
                            })
                        }
                    } else {
                        wx.hideLoading({
                            success: (res) => {},
                        })
                        wx.showToast({
                            title: "服务返回内容异常，请重试",
                            duration: 2000,
                            icon: "none"
                        })
                    }
                },
                fail: err => {
                    console.error('ASR Error:', err)
                    console.log(err)
                    wx.hideLoading({
                        success: (res) => {},
                    })
                    wx.showToast({
                        title: "识别服务请求异常",
                        icon: "none",
                        duration: 2000
                    })
                }
            })
        });

    },

    // 点击输入框时切换TTS并使得输入框可编辑
    onSwitchTTS() {
        // 如果当前是非播放的状态则可切换
        if (!this.data.switchTTS) {
            console.log("切换为播放模式")
            this.setData({
                switchTTS: true,
                tip: "点击播放按钮进行语音合成"
            })
        }
    },

    //文本框监听
    bindTextAreaInput: function (e) {
        console.log("更新文本")
        this.setData({
            asrResult: e.detail.value
        })
    },

    //长按播放按钮切换回录音模式
    onSwitchRecord() {
        console.log("切换为录音模式")
        this.setData({
            switchTTS: false,
            tip: "按住麦克风按钮，然后开始录音"
        })
    },

    // 闽南语语音合成
    playStart() {
        console.log(this.data.asrResult)
        if (this.data.asrResult == "") {
            wx.showToast({
                title: "请输入要进行语音合成的文本",
                icon: "none",
                duration: 2000
            })
            return
        }

        wx.showLoading({
            title: "加载中"
        })

        const innerAudioContext = this.data.innerAudioContext;

        // 使用统一的TTS请求函数，闽南语参数：spkid=0, lanid=3
        ttsRequest(this.data.asrResult, {
            spkid: 0,
            lanid: 3
        }).then(result => {
            wx.hideLoading()

            // 播放音频
            innerAudioContext.src = result.audioUrl
            innerAudioContext.play()

        }).catch(error => {
            wx.hideLoading()
            console.error('TTS Error:', error)
            handleTtsError(error, true)
        })
    },

    //按键点击(暂停播放)
    playPaused: function () {
        this.data.innerAudioContext.pause();
    },

    //按键点击(继续播放)
    playGoOn() {
        this.data.innerAudioContext.play();
    },

    // 初始化innerAudioContext
    initAudioContext() {
        const innerAudioContext = wx.createInnerAudioContext();
        innerAudioContext.useWebAudioImplement = true;
        //设置全局播放选项
        wx.setInnerAudioOption({
            obeyMuteSwitch: false,
            mixWithOther: false,
        });
        this.setData({
            innerAudioContext: innerAudioContext
        })

        //监听音频播放事件
        innerAudioContext.onPlay(() => {
            console.log('监听播放')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'playing',
            })
        })

        //监听音频自然播放至结束的事件
        innerAudioContext.onEnded((res) => {
            console.log('音频播放结束')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'stop',
            })
        });

        //监听音频暂停事件
        innerAudioContext.onPause((res) => {
            console.log('播放暂停')
            this.setData({
                'audioStatus': 'paused',
            })
        })

        //监听音频停止事件
        innerAudioContext.onStop((res) => {
            console.log('播放停止')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'stop',
            })
        })

        //监听音频播放错误事件
        innerAudioContext.onError((res) => {
            console.log('播放错误', err)
            this.data.innerAudioContext.stop();
            wx.hideLoading({
                success: (res) => {},
            })
            wx.showToast({
                title: "音频播放异常，请重试。",
                icon: "none",
                duration: 2000
            })
        })
    },

    onShow() {
        // 记录客户端类别
        var res = wx.getSystemInfoSync()
        this.setData({
            currentClient: res.platform
        })
        this.initAudioContext() // 初始化播放
        // 检查录音接口是否可用
        wx.getSetting({
            success(res) {
                if (!res['scope.record']) {
                    // 接口调用询问
                    wx.authorize({
                        scope: 'scope.record',
                        fail() {
                            wx.openSetting({
                                success: (res) => {
                                    res.authSetting = {
                                        "scope.record": true
                                    }
                                }
                            })
                        },
                        complete() {}
                    })
                }
            }
        })
        this.initAudioContext()
        this.initRecorderManager()
    },

    stopRecord: function () {
        this.setData({
            'isluyin': false,
        })
        this.data.recorderManager.stop()
    },

    startRecord() {
        let _this = this;
        // 检查接口是否可用
        wx.getSetting({
            success(res) {
                if (!res['scope.record']) {
                    // 接口调用询问
                    wx.authorize({
                        scope: 'scope.record',
                        success(res) {
                            const options = {
                                sampleRate: 16000,
                                frameSize: 16,
                                numberOfChannels: 1,
                                format: 'wav'
                            }
                            _this.setData({
                                'isluyin': true,
                            })
                            _this.data.recorderManager.start(options);
                            // _this.data.recorderManager.start();  // PC端不给参数 测试音频是否正常
                        },
                        fail() {
                            wx.showToast({
                                title: '录音权限未开启',
                                icon: 'error'
                            })
                        },
                        complete() {}
                    })
                }
            }
        })
    },

    /*new code from now on */
    onShareAppMessage: function (res) {
        return {
            title: '天聪智能演示平台',
            path: '/pages/tts1/index'
        }
    },

    onShareTimeline: function (res) {
        return {
            title: '天聪智能演示平台',
            path: '/pages/tts1/index'
        }
    }
})