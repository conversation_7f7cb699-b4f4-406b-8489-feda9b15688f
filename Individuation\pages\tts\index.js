//获取应用实例
var common = require("./../../utils/util.js")
var log = require("../../dist/js/log.js")
const app = getApp()
const {
    ttsRequest,
    handleTtsError,
    AudioManager
} = common
Page({
    data: {
        //语音播放上下文
        innerAudioContext: {},
        //音频管理器
        audioManager: null,
        //语音播放状态
        audioStatus: "stop",
        //文本框默认内容
        textcontent: '厦门是一座美丽的海滨城市。',
        //当前音频链接
        currentAudioUrl: "",
        //多语种场景 角色数据
        multilanguageSpks: null,
        //多语种场景 语种数据
        multilanguageIds: null,
        //播放按键自适应高度
        height: null,
        //语言按键激活标识
        currentActiveName: "小樱",
        //当前多语种SpkId 默认为空 - 与Flask项目保持一致
        currentMultilangSpkId: 0,
        //当前多语种语种id 默认为空 - 与Flask项目保持一致，0=普通话
        currentMultilangId: 0,
        //是否已经合成播放过音频
        isTTSOnce: false,
        //是否正在合成中
        isSynthesizing: false
    },

    //生命周期函数--监听页面加载
    onLoad: function (options) {
        this.initAudioContext() // 初始化播放器

        // 初始化音频管理器
        const audioManager = new AudioManager()
        audioManager.init()
        this.setData({
            audioManager: audioManager
        })

        wx.showShareMenu({
            withShareTicket: true
        })

        //加载全局数据
        this.setData({
            multilanguageSpks: app.globalData.multilanguageSpks,
            multilanguageIds: app.globalData.multilanguageIds,
            height: (wx.getSystemInfoSync().windowHeight - 190 - 157) * 2
        })
    },

    //文本框监听
    bindTextAreaBlur: function (e) {
        this.setData({
            'textcontent': e.detail.value
        })
    },
    //开始播放
    playStart: function () {
        console.log("点击播放按键")
        wx.showLoading({
            title: '加载中',
        })
        const _this = this
        var textcontent = _this.data.textcontent;
        const innerAudioContext = _this.data.innerAudioContext;

        // 判断未输入
        if (!textcontent) {
            wx.showToast({
                title: '请输入想要进行语音合成的字段',
                duration: 1500,
                icon: 'none'
            })
            return
        }

        //判断字数上限
        if (textcontent.length > 1000) {
            wx.showToast({
                title: '输入的字段不能大于1000字',
                duration: 1700,
                icon: 'none'
            })
            return
        }

        // 设置合成状态
        _this.setData({
            isSynthesizing: true
        })

        var multilangSpkId = _this.data.currentMultilangSpkId
        var multilangId = _this.data.currentMultilangId

        // 使用统一的TTS请求函数
        ttsRequest(textcontent, {
            spkid: parseInt(multilangSpkId),
            lanid: parseInt(multilangId)
        }).then(result => {
            wx.hideLoading()

            // 使用音频管理器播放
            if (_this.data.audioManager) {
                _this.data.audioManager.play(result.audioUrl)
            } else {
                // 兼容旧版本
                innerAudioContext.src = result.audioUrl
                innerAudioContext.play()
            }

            _this.setData({
                currentAudioUrl: result.audioUrl,
                isTTSOnce: true,
                isSynthesizing: false
            })

        }).catch(error => {
            wx.hideLoading()
            log.error(error) // 实时日志，后台可查询
            handleTtsError(error)

            _this.setData({
                isSynthesizing: false
            })
        })
    },

    //按键点击(暂停播放)
    playPaused: function () {
        if (this.data.audioManager) {
            this.data.audioManager.pause();
        } else {
            this.data.innerAudioContext.pause();
        }
    },

    //按键点击(继续播放)
    playGoOn() {
        if (this.data.audioManager && this.data.currentAudioUrl) {
            this.data.audioManager.play(this.data.currentAudioUrl);
        } else {
            this.data.innerAudioContext.play();
        }
    },

    // 初始化innerAudioContext（PC版小程序切换图标和场景时有时音频不会改变，可能是bug）
    initAudioContext() {
        var innerAudioContext = wx.createInnerAudioContext();
        innerAudioContext.useWebAudioImplement = true;
        this.setData({
            innerAudioContext: innerAudioContext
        })
        this.registerInnerAudioContext(); // 注册回调函数
    },


    //按键点击(更改点亮的按钮)
    onChangeActive: function (event) {
        // 停止当前播放
        if (this.data.audioManager) {
            this.data.audioManager.stop()
        } else {
            this.data.innerAudioContext.stop()
        }

        this.setData({
            currentActiveName: event.currentTarget.dataset.name,
            currentMultilangSpkId: event.currentTarget.dataset.spkid
        })
    },

    //多语种场景 选择语种点击
    onChangeActiveMultilang: function (event) {
        // 停止当前播放
        if (this.data.audioManager) {
            this.data.audioManager.stop()
        } else {
            this.data.innerAudioContext.stop()
        }

        this.setData({
            currentMultilangId: event.currentTarget.dataset.langid
        })
    },
    //标签切换(首个按键点亮)
    onTabChange: function (event) {
        // 停止当前播放
        if (this.data.audioManager) {
            this.data.audioManager.stop()
        } else {
            this.data.innerAudioContext.stop()
        }

        this.setData({
            currentActiveName: '小樱', // 与Flask项目保持一致，默认选择小樱
            currentLangType: 'multilanguage',
            currentMultilangSpkId: '0', // 小樱的spkId是0
            currentMultilangId: '0', // 普通话
        })
    },

    //注册音频
    registerInnerAudioContext: function () {
        let innerAudioContext = this.data.innerAudioContext

        //监听音频播放事件
        innerAudioContext.onPlay(() => {
            // 如果是首次播放，那么置一个标志位
            if (!this.data.isTTSOnce) {
                this.setData({
                    isTTSOnce: true
                })
            }
            console.log('监听播放')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'playing',
            })
        })

        //监听音频自然播放至结束的事件
        innerAudioContext.onEnded((res) => {
            console.log('音频播放结束')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'stop',
            })
        });

        //监听音频暂停事件
        innerAudioContext.onPause((res) => {
            console.log('播放暂停')
            this.setData({
                'audioStatus': 'paused',
            })
        })

        //监听音频停止事件
        innerAudioContext.onStop((res) => {
            console.log('播放停止')
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'stop',
            })
        })

        //监听音频播放错误事件
        innerAudioContext.onError((res) => {
            // IOS在首次进入语音合成页面时，如果播放器没有播放过，
            // 然而直接进行stop，此时会触发onError，
            // 目前未知发生原因，因为播放器已在onLoad进行初始化过了
            // 这里进行一个问题兼容
            // 如果还没播放过音频，那么不给出错误提示(可能导致其它问题)
            console.log("音频播放错误: ", res)
            if (!this.data.isTTSOnce) {
                return
            }
            console.log('播放错误', res)
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                'audioStatus': 'stop',
            })
            wx.showToast({
                title: '语音播放异常，请重试',
                duration: 2000,
                icon: "none"
            })
        })
    },

    // 监听音频因为受到系统占用而被中断开始事件。
    onAudioInterruptionBegin: function () {
        this.setData({
            'audioStatus': 'stop',
        })
        this.data.innerAudioContext.pause();
    },

    // 监听音频因为受到系统占用而被中断开始事件结束。
    onAudioInterruptionBegin: function () {
        this.data.innerAudioContext.start();
        this.setData({
            'audioStatus': 'playing',
        })
    },

    // 允许分享
    onShareAppMessage: function (res) {
        return {
            title: '天聪智能演示平台',
            path: '/pages/tts/index'
        }
    },

    // 允许分享到朋友圈
    onShareTimeline: function (res) {
        return {
            title: '天聪智能演示平台',
            path: '/pages/tts/index'
        }
    },

    // 页面卸载时清理资源
    onUnload: function () {
        // 销毁音频管理器
        if (this.data.audioManager) {
            this.data.audioManager.destroy()
        }

        // 销毁旧的音频上下文
        if (this.data.innerAudioContext) {
            this.data.innerAudioContext.destroy()
        }
    }
})