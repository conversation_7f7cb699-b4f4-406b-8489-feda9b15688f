// pages/i_model/index.js
const app = getApp()
var common = require("./../../dist/js/common.js")
Page({
    /**
     * 页面的初始数据
     */
    data: {
        modelNameRules: [{
                required: true,
                message: "请输入模型名称",
                trigger: "blur"
            },
            {
                min: 2,
                max: 15,
                message: "名称长度在2-15个字符之间",
                trigger: "blur"
            },
            {
                pattern: '^[A-Za-z0-9]+$',
                message: '名称必须由数字字母组成',
                trigger: 'blur'
            }
        ],
        audioTempFilePath: ""
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // 使用linui表单需要初始化
        wx.lin.initValidateForm(this)
        this.setData({  // 接收record2页面传入的录音文件的临时路径
            audioTempFilePath: options.tempFilePath,
        })
    },

    onModelFormSubmit(event) {
        const { detail } = event;
        if (detail.isValidate == false) {  // 表单验证没通过就return
            return
        }
        wx.showLoading({
          title: '加载中',
        })
        const { modelName } = detail.values;
        common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/util/wechat/query_model_all", {}, {
            token: app.globalData.userInfo.token
        }, "GET").then(res => {
            let existTag = false
            // 响应成功
            if (res.data.state == "success"){
                // xvector类的模型
                res.data.xvector_models.forEach((item, index, err) => {
                    console.log("item: ", item)
                    if (item == modelName ){
                        existTag = true
                        wx.showToast({
                           title: '模型名称已存在',
                           icon: "error",
                           duration: 2000
                        })
                    }
                })
            }
            if (!existTag) {
                // 如果名称合法 就可以上传文件了
                wx.uploadFile({
                    url: "https://tc.talentedsoft.com:58123/individuation/api/xvector/create_model",
                    filePath: this.data.audioTempFilePath,
                    name: 'file',
                    formData: {
                        model_name: modelName,  // 小程序未命名模型默认值
                    },
                    header: {
                        "Content-Type": "multipart/form-data",
                        "Authorization": "Bearer " + app.globalData.userInfo.token // 后端通过 Bearer Auth
                    },
                    success: function (res) {
                        if (res.data) {
                            var data = JSON.parse(res.data)
                            if (data.state == "success") {
                                wx.hideLoading({
                                    success: (res) => {},
                                })
                                wx.showToast({
                                    title: "上传文件成功",
                                    icon: "success"
                                })
                                wx.switchTab({
                                    url: '../i_tts/index',
                                  })
                            } else {
                                wx.hideLoading({
                                    success: (res) => {},
                                })
                                wx.showToast({
                                    title: "上传文件失败, 请尝试重试录音。",
                                    icon: "none",
                                    duration: 2000
                                })
                                wx.switchTab({
                                    url: '../i_record2/index',
                                  })
                            }
                        }
                    },
                    fail: err => {
                        console.log(err)
                        wx.hideLoading({
                            success: (res) => {},
                        })
                        wx.showToast({
                            title: "上传文件接口请求异常",
                            icon: "none",
                            duration: 2000
                        })
                    }
                })
            }
        }).catch(err => {
            console.log(err)
            // 服务异常就不跳转
            wx.hideLoading({
                success: (res) => {},
            })
            wx.showToast({
                title: '服务异常',
                duration: 2000
            })
        })
    },

    onRedirectIndex() {
        wx.switchTab({
          url: '../tts/index',
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})