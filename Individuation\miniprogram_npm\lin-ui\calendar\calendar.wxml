<wxs src="./index.wxs" module="computed"></wxs>

<template name="calendar">
  <view class="calendar-container">
    <header showTitle="{{ showTitle }}" showSubtitle="{{ showSubtitle }}" title="{{ title }}" subTitle="{{ subTitle }}"></header>
    <scroll-view class="calendar-body-wrap" scroll-y scroll-into-view="{{ scrollIntoViewIndex }}">
      <mounth wx:for="{{ computed.getMonths(minDate, maxDate) }}" wx:key="index" id="month{{ index }}" class="month" data-date="{{ item }}" date="{{ item }}" minDate="{{ minDate }}" maxDate="{{ maxDate }}" currentDate="{{ currentDate }}" type="{{ type }}" bind:clickDay="clickDay" showMonthTitle="{{ index !== 0 }}" formatter="{{ formatter }}" color="{{ color }}">
      </mounth>
    </scroll-view>
    <view>
      <l-button type="default" l-class="bottom-button" size="long" bind:lintap="onClickConfirm" bg-color="{{ color }}">{{confirmText}}</l-button>
    </view>
  </view>
</template>
