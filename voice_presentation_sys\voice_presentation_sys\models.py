# -*- coding: utf-8 -*-
from datetime import datetime

from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

from voice_presentation_sys.extensions import db


class User(db.Model, UserMixin):

    __tablename__ = 'voiceprint_user'
    id = db.Column(db.Integer, primary_key=True)
    userid = db.Column(db.String(30))
    register_type = db.Column(db.String(20), nullable=False)
    register_word = db.Column(db.String(20), nullable=True)


    # password_hash = db.Column(db.String(128))
    #
    # def set_password(self, password):
    #     self.password_hash = generate_password_hash(password)
    #
    # def validate_password(self, password):
    #     return check_password_hash(self.password_hash, password)


# class WavAndContent(db.Model):
#
#     __tablename__ = 'wav_and_content'
#     id = db.Column(db.<PERSON><PERSON><PERSON>, primary_key=True)
#     wav_type = db.Column(db.String(30))
#     wav_name = db.Column(db.String(30))
#     wav_content = db.Column(db.String(256))
#     modi_date = db.Column(db.DateTime, default=datetime.utcnow, comment='修改时间')
