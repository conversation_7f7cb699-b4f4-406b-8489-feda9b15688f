# -*- coding: utf-8 -*-
import os, requests, json, re, traceback, time, subprocess, sys, uuid, io
from subprocess import CalledProcessError
import sys
import threading
from requests import HTTPError

from flask import request, current_app, Blueprint, jsonify, url_for, send_file, Response
from sqlalchemy import and_

from voice_presentation_sys.utils import general_requests_func
from voice_presentation_sys.extensions import db
from voice_presentation_sys.models import User


ajax_bp = Blueprint('ajax', __name__)

# 设置日志
import logging
logger = logging.getLogger("ajax_logger")
logger.setLevel(logging.INFO)
# 创建日志处理器（只设置一次）
file_handler = logging.FileHandler("logs/ajax_requests.log", encoding="utf-8")
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

@ajax_bp.route('/tts', methods=['POST'])
def word_tts():
    """
    语音合成
    根据post请求内携带的按键id，选择不同语言类型和人声端口进行http请求。
    """
    url = current_app.config['WORD_TTS_URL']
    get_wav_url = current_app.config['WORD_TTS_GET_WAV_PATH_URL']
    spk_id = int(request.values.get('spk_id'))  # 说话人id
    lan_id = int(request.values.get('lan_id'))  # 发音类型id
    content = request.values.get('content')

    # 测试链接
    if lan_id == 1:
        url = "https://tc.talentedsoft.com:58124/58104/dotctts"
        get_wav_url = "https://tc.talentedsoft.com:58124/58104/tts/"
        lan_id = 3

    valid_id_list = [0, 1, 2, 3, 4, 5, 6, 7]  # 合法的spk_id范围
    if spk_id not in valid_id_list:
        return jsonify(
            state="Error",
            msg=f"不合法的发音类别，{spk_id}"
        )

    params = {'userid': os.getenv('USERID'), 'token': os.getenv('TOKEN'), 'content': content,
              'spkid': spk_id, 'lanid': lan_id}
    
    # if params['spkid'] == 1：


    try:
        res = requests.post(url=url, data=params)
    except Exception as e:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="tts服务请求失败"
        )

    try:
        # 这里获取wavfile  ==>  res.json()['wavfile']
        pattern_get_wavfile = r"\"wavfile\":\"(\d+/\d+/\d+_\d+.wav)\""
        w = re.search(pattern_get_wavfile, res.text)
        tmp1 = '{' + w.group() + '}'
        res_wavfile = json.loads(tmp1)['wavfile']
    except:
        res_wavfile = res.json()['wavfile']

    try:
        # 这里获取errCode  ==>  res.json()['errCode']
        pattern_get_errCode = r"\"errCode\":\"(\d+?)\""
        e = re.search(pattern_get_errCode, res.text)
        tmp2 = '{' + e.group() + '}'
        res_errCode = json.loads(tmp2)['errCode']
    except:
        res_errCode = res.json()['errCode']

    if res_errCode != '0':
        current_app.logger.warn(f"tts服务异常, errCode:{res_errCode}.")
        return jsonify(
            state="Error",
            msg="合成异常，errCode:{res_errCode}."
        )
    elif res_errCode == '0':
        return jsonify(
            state="Success",
            msg=get_wav_url + '/' + res_wavfile
        )
        
@ajax_bp.route('proxy_tts', methods=['GET', 'POST'])
def proxy_tts():
    """
    TTS（支持中文双语）的代理接口
    所需参数如下：
        spkid ：说话人id，合法的说话人id为[0,1,2,3,4,5,6]
        lanid ：语种id，合法的说话人id为[0,4,5,6]
        content ：合成的文本内容
    """
    # 取出前端请求参数
    # userid = request.args.get('userid', 'yhy')
    # token = request.args.get('token', '1234')
    spkid = request.args.get('spkid', '0')
    lanid = request.args.get('lanid', '0')
    content = request.args.get('content', '')
    logger.info(f"[proxy_tts]Received AJAX GET: spkid={spkid}, lanid={lanid}, content={content}")

    # 目标TTS服务URL和参数构建
    target_url = current_app.config['BILINGUAL_WORD_TTS_URL']
    if lanid == '4':
        target_url = current_app.config['TW_TTS_URL']
        spkid = '0'
    elif lanid == '5':
        spkid = '7'
    elif lanid == '6':
        spkid = '8'
    params = {
        'userid': 'yhy',
        'spkid': spkid,
        'token': '1234',
        'content': content,
    }

    try:
        # 向目标服务器发起请求，响应为流
        resp = requests.get(target_url, params=params, stream=True, timeout=15)
        resp.raise_for_status()
    except Exception as e:
        return {"state": "Error", "msg": f"TTS服务请求失败: {str(e)}"}, 500

    # 直接将目标服务器响应的数据流和头部信息转发给前端
    headers = {
        'Content-Type': resp.headers.get('Content-Type', 'application/octet-stream'),
        'Content-Length': resp.headers.get('Content-Length', ''),
        # 必要时添加其它响应头
    }

    return Response(resp.raw, headers=headers, status=resp.status_code)

@ajax_bp.route('/asr', methods=['POST'])
def wav_asr():
    """
    语音识别
    :param sex_asr_sign: 标志位, 为True是为性别识别, 为False则为语音识别
    :param file_tyle: 标志位, upload_file是为此次为上传文件，record_file是为此次为实时录音文件
    """
    params = {'userid': os.getenv('USERID'), 'token': os.getenv('TOKEN')}
    if request.values.get('file_type') == 'upload_file':
        file = request.files['wav_file'].read()
    else:
        file = request.files['wav_blob'].read()
           
    sex_asr_sign = False
    if request.values.get('type') == 'sex-asr':
        url = current_app.config['WAV_SEX_ASR_URL']
        sex_asr_sign = True
    elif request.values.get('type') == 'asr':
        url = current_app.config['WAV_ASR_URL']
    else:
        url = current_app.config['MINNAN_WAV_ASR_URL']
        
    try:
        session = requests.Session()
        res = session.post(url=url, data=params, files={'file': file})
        res_json = json.loads(res.text, strict=False)
    except:
        current_app.logger.warn(traceback.format_exc())

    if sex_asr_sign:
        res_json_err = '0'  # 目前性别识别只返回sex=0或1
    else:
        res_json_err = res_json['errCode']

    if res_json_err != '0':  # 语音不行保存下来看看
        time_now = str(int(time.time()))
        save_path = os.path.join(current_app.config['ASR_INVALID_DIR'], time_now)
        with open(save_path + '.wav', mode='wb') as f:
            f.write(file)
        current_app.logger.warn(f"asr返回错误码有误 errCode:{res_json_err} path:{save_path}")
        return jsonify(
            state="Error",
            msg="Server error."
        )
    elif res_json_err == '0':
        if sex_asr_sign:
            sex = res_json['sex']
            if sex == 0:
                content = '男'
            elif sex == 1:
                content = '女'
            else:
                content = '服务器出错，请重试'
        else:
            content = res_json['result']
        return jsonify(
            state="Success",
            msg=content
        )

@ajax_bp.route('/asrfile', methods=['POST'])
def wav_asrfile():
    """
    语音识别 
    :param sex_asr_sign: 标志位, 为True是为性别识别, 为False则为语音识别
    :param file_tyle: 标志位, upload_file是为此次为上传文件，record_file是为此次为实时录音文件
    """
    params = {'userid': os.getenv('USERID'), 'token': os.getenv('TOKEN')}
    if request.values.get('file_type') == 'upload_file':
        file = request.files['wav_file'].read()
    else:
        file = request.files['wav_blob'].read()

    sex_asr_sign = False
    if request.values.get('type') == 'sex-asr':
        url = "http://122.51.242.54:9385/dotcasr" #粤语
    elif request.values.get('type') == 'asr':
        url = "http://125.77.202.194:9395/dotcasr"  #中文
    else:
        url = "http://122.51.242.54:9382/dotcasr"   #英语
        
    try:
        session = requests.Session()
        res = session.post(url=url, data=params, files={'file': file})
        res_json = json.loads(res.text, strict=False)
    except:
        current_app.logger.warn(traceback.format_exc())

    if sex_asr_sign:
        res_json_err = '0'  # 目前性别识别只返回sex=0或1
    else:
        res_json_err = res_json['errCode']

    if res_json_err != '0':  # 语音不行保存下来看看
        time_now = str(int(time.time()))
        save_path = os.path.join(current_app.config['ASR_INVALID_DIR'], time_now)
        with open(save_path + '.wav', mode='wb') as f:
            f.write(file)
        current_app.logger.warn(f"asr返回错误码有误 errCode:{res_json_err} path:{save_path}")
        return jsonify(
            state="Error",
            msg="Server error."
        )
    elif res_json_err == '0':
        content = res_json['result']
        return jsonify(
            state="Success",
            msg=content
        )
@ajax_bp.route('/voiceprint', methods=['POST'])
def voiceprint():
    """
    声纹页面的所有ajax操作的错误处理和正确返回。
    """

    # 获取当前配置
    operation_type = request.values.get('operation_type')
    if operation_type == 'text-independent':
        current_config = current_app.config['TEXT_INDEPENDENT']
    elif operation_type == 'text-related':
        current_config = current_app.config['TEXT_RELATED']
    else:  # operation_type == 'dynamic-password'
        current_config = current_app.config['DYNAMIC_PASSWORD']

    # 获取上传的语音文件并读取
    if request.values.get('file_type') == 'upload_file':
        file = request.files['wav_file'].read()  # wav_file是上传文件的默认名称
    else:  # 此时request.values.get('file_type') == 'record_file':
        file = request.files['wav_blob'].read()

    userid = request.values.get('userid')
    content = "67032589"
    if operation_type != "dynamic-password":               
            content = "123456"  #
    
    # 判断operation值是否为"null"，如是则为声纹登记操作(区别为文本无关，相关，口令)，否则为 声纹确认/声纹辨认的操作
    operation = request.values.get('operation')
    operation_type = request.values.get('operation_type')  # 获取operation_type

    if operation != "null":
        if operation == 'voiceprint-confirm':
            #res_json = general_requests_func(url=current_config['CONFIRM_URL'], userid=userid, token=current_config['TOKEN'], content=content, file=file)
            res_json = requests.post(url=current_config['CONFIRM_URL'], data={'userid': userid, 'token': current_config['TOKEN'], 'content': content}, files={'file': file}).json() #yhy2024-03-27
            if res_json['errCode'] == 0 and float(res_json['score']) >= 0.6:#yhy2024-05-28
                # 仅对 dynamic-password 进行校验  
                if operation_type == 'dynamic-password':
                    # 获取识别的数字数量
                    numwords = res_json.get('numwords')
                    expected_numwords = 8
                    # 计算错误的数字数量（期望数量 - 识别数量）
                    error_count = abs(expected_numwords - numwords)
                    if error_count <= 2:
                        return jsonify(
                            msg="声纹确认成功，当前语音确认结果分数为: " + str(res_json['score'])
                        )
                    else:
                        return jsonify(
                            msg="声纹确认失败，请重新录音。数字念错:" + str(error_count)+"个字"
                        )
                else:
                    print('现在测试的不是动态口令')
                    return jsonify(
                        msg="声纹确认成功，当前语音确认结果分数为: " + str(res_json['score'])
                    )
            else:
                return jsonify(
                    msg="声纹确认失败，语音确认结果不合格。分数为: " + str(res_json['score'])
                )

        elif operation == 'voiceprint-recognition':
            res_json = general_requests_func(url=current_config['RECOGNITION_URL'], userid=userid,
                                             token=current_config['TOKEN'], content=content, file=file)

            if res_json['errCode'] == 0:
                if operation_type == 'dynamic-password':
                    numwords = res_json.get('numwords')
                    expected_numwords = 8

                    # 计算错误的数字数量（期望数量 - 识别数量）
                    error_count = abs(expected_numwords - numwords)

                    if error_count < 2:
                        pattern = r'\[.*\]'
                        id_list = re.search(pattern, res_json['msg'])
                        return jsonify(
                            msg="当前语音辨认结果最高分ID为: " + id_list.group()[1:-1].split(" ")[0]
                        )
                    else:
                        return jsonify(
                            msg="声纹辨认失败，数字念错，请重新录音。"
                        )
                else:
                    print('现在测试的不是动态口令')
                    pattern = r'\[.*\]'
                    id_list = re.search(pattern, res_json['msg'])
                    return jsonify(
                        msg="当前语音辨认结果最高分ID为: " + id_list.group()[1:-1].split(" ")[0]
                    )
            else:
                return jsonify(
                    msg="声纹辨认失败，请重试。"
                )

        else:
            return jsonify(
                msg='当前页面出错，请刷新页面后重试。'
            )

    else:
        # 注册用户
        res_json = general_requests_func(url=current_config['REGISTER_URL'], userid=userid,
                                         token=current_config['TOKEN'])
        if res_json['errCode'] != 0:
            return jsonify(
                msg='用户注册失败，请更换userid再次重试。'
            )
        # 默认口令
        content = "123456"
        # 数据库录入用户，以便在前端的声纹确认/辨认，给出注册时的口令/文本
        if operation_type == "text-independent":
            user = User(userid=userid, register_type=operation_type)
        elif operation_type == "text-related":
            user = User(userid=userid, register_type=operation_type, register_word="智能软件")
        else:
            user = User(userid=userid, register_type=operation_type, register_word="67032589")
        db.session.add(user)
        db.session.commit()

        # 当前注册通过，然后添加语音+训练模型。
        res_json = requests.post(url=current_config['ADD_SAMPLE_URL'], data={'userid': userid,  # 添加语音
                                                                             'token': current_config['TOKEN'], 'step': 1, 'content': content}, files={'file': file}).json()

        if res_json['errCode'] == 0:
            res_json = general_requests_func(url=current_config['TRAIN_MODEL_URL'], userid=userid,  # 训练模型
                                             token=current_config['TOKEN'])

            if res_json['errCode'] == 0:
                return jsonify(
                    msg="用户注册成功，模型生成完毕。"
                )
            else:
                # 模型训练失败则删除用户(线上模型和数据库都要删除)，提示重新注册。
                requests.post(url=current_config['DEL_USER_URL'], data={'userid': userid, 'token': current_config['TOKEN']})
                user = User.query.filter(and_(User.userid == userid, User.register_type == operation_type)).first()
                db.session.delete(user)
                db.session.commit()

                return jsonify(
                    msg="训练模型失败，请重新注册。"
                )
        else:
            # 语音添加失败则删除用户(线上模型和数据库都要删除)，提示重新注册。
            requests.post(url=current_config['DEL_USER_URL'], data={'userid': userid, 'token': current_config['TOKEN']})
            user = User.query.filter(and_(User.userid == userid, User.register_type == operation_type)).first()
            db.session.delete(user)
            db.session.commit()

            return jsonify(
                msg="添加语音失败，请重新注册，若仍为相同错误，请联系管理员。"
            )


# 这个函数用来查询用户的口令or文本
@ajax_bp.route('/detect_user_register_word/<string:userid>/<string:register_type>', methods=["GET"])
def detect_user_register_word(userid, register_type):
    try:
        user = User.query.filter(and_(User.userid == userid, User.register_type == register_type)).first()
        return jsonify(
            state='Success',
            msg=user.register_word
        )
    except:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state='Error',
            msg='Server error.'
        )


# 这个函数用数据库userid来验证是否注册(未注册或存在多个用户则返回)
@ajax_bp.route('/detect_user_register/<string:userid>/<string:register_type>', methods=["GET"])
def detect_user_register(userid, register_type):
    try:
        user_list = User.query.filter(and_(User.userid == userid, User.register_type == register_type)).all()
        if len(user_list) >= 1:
            return jsonify(
                state="Error"
            )
        else:
            return jsonify(
                state="Success"
            )
    except:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state='Error',
            msg='Server error.'
        )


@ajax_bp.route('/tts2/dotctraintts', methods=['POST'])
def train_tts():
    subuser = request.values.get('subuser')
    token = os.getenv('TOKEN')
    userid = os.getenv('USERID')

    file_type = request.values.get('file_type')
    if file_type:
        if file_type == 'upload_file':
            file = request.files['wav_file'].read()
        else:  # record_file
            file = request.files['wav_blob'].read()
    else:
        return jsonify(
            state="Error",
            msg="file_type is invalid."
        )

    params = {'userid': userid, 'token': token, 'subuser': subuser}

    url = current_app.config['TRAIN_TTS_URL']
    try:
        res = requests.post(url=url, data=params, files={'file': file})
        res_json = json.loads(res.text, strict=False)
    except:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="服务异常"
        )

    res_json_err = res_json['errCode']

    if res_json_err != '0':  # 语音不行保存下来看看
        current_app.logger.warn(f"train_tts 返回错误码 {res_json}")
        return jsonify(
            state="Error",
            msg="训练异常"
        )
    elif res_json_err == '0':
        return jsonify(
            state="Success",
            msg=res_json['result']
        )


@ajax_bp.route('/tts2/dotcgentts', methods=['GET', 'POST'])
def gen_tts():
    subuser = request.values.get('subuser')
    content = request.values.get('content')
    userid = os.getenv('USERID')
    token = os.getenv('TOKEN')

    params = {'userid': userid, 'token': token, 'content': content, 'subuser': subuser}
    url = current_app.config['GEN_TTS_URL']
    try:
        res = requests.get(url=url, params=params)
        if res.status_code != 200:
            raise HTTPError
    except Exception as e:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="合成语音异常"
        )
    else:
        return send_file(
            io.BytesIO(res.content),
            mimetype="audio/wav",
            cache_timeout=0  # 不要缓存文件，否则同样的请求将复用之前的文件内容。
        )


@ajax_bp.route('/denoise', methods=['POST'])
def denoise():
    """
    语音降噪
    """
    params = {'userid': os.getenv('USERID'), 'token': os.getenv('TOKEN')}
    if request.values.get('file_type') == 'upload_file':
        file = request.files['wav_file'].read()
    else:
        file = request.files['wav_blob'].read()

    try:
        res = requests.post(url=current_app.config["DENOISE_URL"], data=params, files={'file': file})
        print(current_app.config["DENOISE_URL"])
        print("res: ", res.content)
        res_json = json.loads(res.text, strict=False)
    except Exception as e:
        current_app.logger.warn(f"denoise请求异常: {traceback.format_exc()}")
        return jsonify(
            state="Error",
            msg="降噪服务请求失败"
        )

    res_json_err = res_json['errCode']

    if res_json_err != '0':  # 语音不行保存下来看看
        current_app.logger.warn(f"denoise返回错误码有误 errCode:{res_json_err}")
        return jsonify(
            state="Error",
            msg="Server error."
        )
    else:
        return jsonify(
            state="Success",
            msg=res_json["wavfile"]
        )


@ajax_bp.route('/denoise/wav', methods=['GET', 'POST'])
def denoise_get_wav():
    wavfile = request.values.get('wavfile')
    url = current_app.config["GET_DENOISE_WAV"] + '/' + wavfile
    print(url)
    try:
        res = requests.get(url=url)
        status_code = res.status_code
        if status_code != 200:
            if status_code == 404:
                return jsonify(
                    state="Error",
                    msg="语音暂未生成"
                ), 404
            else:
                raise HTTPError
        else:
            pass
    except Exception as e:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="语音处理异常"
        ), 404

    return send_file(
        io.BytesIO(res.content),
        mimetype="audio/wav",
        cache_timeout=0  # 不要缓存文件，否则同样的请求将复用之前的文件内容。
    ), 200
    
@ajax_bp.route('/voice_clone', methods=['POST'])
def voice_clone():
    """
    音色克隆
    """
    try:
        text = request.form.get('text', '').strip()
        file = request.files.get('wav_file')
        
        if not text or not file:
            return jsonify({"code": 1, "msg": "缺少文本或音频"}), 400
        
        # 构造 TTS 请求参数
        tts_text = {'text': text}
        tts_files = {'wav_file': (file.filename, file.stream, file.mimetype)}
        
        # 请求 TTS 服务
        url = 'https://znr6jspt-67s1780s-9396.zjrestapi.gpufree.cn:8443/cv2tts'  # 这里之后替换为实际的语音克隆服务URL，现在先用本地测试（已替换）
        res = requests.post(url, data=tts_text, files=tts_files)
        
        # 请求失败：处理错误
        if res.status_code != 200:
            try:               
                resp_data = res.json()
                msg = resp_data.get("msg", "合成失败")
            except Exception as e:
                msg = f"TTS服务返回错误：{e}"
            print(f"请求失败，状态码: {res.status_code}, 错误: {msg}")
            return jsonify({"code": 2, "msg": msg}), 500
        
        # 成功：返回音频二进制流

        return send_file(
            io.BytesIO(res.content),
            mimetype="audio/wav",
            as_attachment=False,
            cache_timeout=0  # 不要缓存文件，否则同样的请求将复用之前的文件内容。
        )
    except Exception as e:
        current_app.logger.warning(traceback.format_exc())
        return jsonify({
            "code": 3,
            "msg": f"服务器异常，语音合成失败，错误：{e}"
        }), 500
        
@ajax_bp.route('/upload', methods=['POST'])
def upload_prompt_audio():
    """
    音色克隆页面用于上传prompt音频
    """
    if request.values.get('file_type') == 'upload_file':
        file = request.files.get('wav_file')
    else:
        file = request.files.get('wav_blob')
        
    if file is None:
        return jsonify({"code": 1, "msg": "文件为空，未接收到上传内容"}), 400
   
    filename = f"record_{int(time.time())}.wav"
    # tmp_dir = '/home/<USER>/voice_presentation_sys/uploads/vc_wavs' # TODO tmp_dir改为从settings.py中读取
    tmp_dir = os.path.join(current_app.config['UPLOADS_DIR'], 'vc_wavs') # 250725更新
    save_path = os.path.join(tmp_dir, filename)
    file.save(save_path)

    return jsonify({
        "code": 0,
        "msg": "上传成功",
        "path": save_path  # 绝对路径
    })
    
@ajax_bp.route('/delete_file', methods=['POST'])
def delete_uploaded_file():
    """
    将上传到前端服务器的过期音频文件删除
    """
    path = request.json.get('path')
    if not path or not os.path.isfile(path):
        return jsonify({"code": 1, "msg": "路径无效或文件不存在"})

    try:
        os.remove(path)
        return jsonify({"code": 0, "msg": "删除成功"})
    except Exception as e:
        return jsonify({"code": 2, "msg": f"删除失败：{str(e)}"})

def clean_uploaded_files(upload_dir='uploads', expire_seconds=3600, check_interval=1800):
    while True:
        now = time.time()
        for filename in os.listdir(upload_dir):
            file_path = os.path.join(upload_dir, filename)
            try:
                if os.path.isfile(file_path):
                    modified = os.path.getmtime(file_path)
                    if now - modified > expire_seconds:
                        os.remove(file_path)
                        print(f"[清理] 删除过期文件：{file_path}")
            except Exception as e:
                print(f"[清理错误] {file_path}: {e}")
        time.sleep(check_interval)

@ajax_bp.before_app_request
def start_cleanup_thread():
    thread = threading.Thread(
        target=clean_uploaded_files,
        args=(os.path.join(current_app.config['UPLOADS_DIR'], 'vc_wavs'),600,), # 250725更新
        daemon=True
    )
    thread.start()
    # print("[启动] 上传文件清理线程已启动")

# 小程序PC端录音无法直接录制成指定语音格式
# 所以PC端的请求额外经过此代理进行语音识别
# 作为语音识别的代理
@ajax_bp.route('/wechat/dotcasr_proxy', methods=['POST'])
def wechat_asr():
    # 区分是普通话识别还是闽南语识别
    asr_type = request.values.get("asr_type")

    # 检查文件是否存在，支持两种字段名
    file = None
    if 'file' in request.files:
        file = request.files['file']
    elif 'wav_file' in request.files:
        file = request.files['wav_file']

    if file is None:
        return jsonify(
            result="未接收到音频文件",
            errCode=-4
        ), 400

    file.seek(0)
    params = {'userid': "tc_wx_yun7_proxy", 'token': "wx5678"}

    # 确保wechat目录存在
    wechat_dir = os.path.join(current_app.config['UPLOADS_DIR'], 'wechat')
    if not os.path.exists(wechat_dir):
        os.makedirs(wechat_dir)

    # 原始文件路径
    raw_file_path = f"{current_app.config['UPLOADS_DIR']}/wechat/raw_{uuid.uuid1().hex}.wav"
    # 转换后的文件路径
    result_file_path = f"{current_app.config['UPLOADS_DIR']}/wechat/result_{uuid.uuid1().hex}.wav"

    try:
        file.save(raw_file_path)
    except Exception as e:
        current_app.logger.error(f"文件保存失败: {e}")
        return jsonify(
            result="文件保存失败",
            errCode=-5
        ), 500

    # 判断平台(开发用的win, 部署在linux)
    WIN = sys.platform.startswith('win')

    # # 临时解决方案：使用系统自带的ffmpeg
    # try:
    #     if WIN:
    #         # Windows下使用系统ffmpeg
    #         process_cmd = [
    #             "ffmpeg",
    #             "-i", raw_file_path,
    #             "-ar", "16000",
    #             "-acodec", "pcm_s16le",
    #             "-ac", "1",
    #             "-y",
    #             "-f", "wav",
    #             result_file_path
    #         ]
    #         subprocess.run(process_cmd, shell=False, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
    #                        check=True, encoding="utf-8")
    #     else:
    #         # Linux下使用系统ffmpeg
    #         process_string = f"ffmpeg -i {raw_file_path} -ar 16000 -acodec pcm_s16le -ac 1 -y -f wav {result_file_path}"
    #         subprocess.run(process_string, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
    #                        check=True, encoding="utf-8")
    # except CalledProcessError as e:
    #     current_app.logger.warn(f"进程状态码: {e.returncode}, 标准错误输出: {e.stderr}, 标准输出: {e.stdout}")
    #     return jsonify(
    #         result="语音转换失败，请更换语音重试。",
    #         errCode=-1
    #     )
    # except FileNotFoundError:
    #     current_app.logger.error("系统中未找到ffmpeg，请安装ffmpeg或检查PATH环境变量")
    #     return jsonify(
    #         result="语音转换服务异常，未找到ffmpeg",
    #         errCode=-2
    #     )

    # 原有的ffmpeg路径代码（已注释）
    if WIN:
        ffmpeg_path = os.path.join(current_app.config['UTIL_FOLDER_PATH'], "ffmpeg_win64.exe")
        shell = False
    else:
        ffmpeg_path = os.path.join(current_app.config['UTIL_FOLDER_PATH'], "ffmpeg_linux")
        shell = True
    if os.path.exists(ffmpeg_path):
        try:
            if WIN:
                # Windows下使用列表格式的命令
                process_cmd = [
                    ffmpeg_path,
                    "-i", raw_file_path,
                    "-ar", "16000",
                    "-acodec", "pcm_s16le",
                    "-ac", "1",
                    "-y",
                    "-f", "wav",
                    result_file_path
                ]
                subprocess.run(process_cmd, shell=shell, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                               check=True, encoding="utf-8")
            else:
                # Linux下使用字符串格式的命令
                process_string = f"{ffmpeg_path} -i {raw_file_path} -ar 16000 -acodec pcm_s16le -ac 1 -y -f wav " \
                                 f"{result_file_path}"  # 16k 单声道 wav
                subprocess.run(process_string, shell=shell, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                               check=True, encoding="utf-8")
        except CalledProcessError as e:
            current_app.logger.warn(f"进程状态码: {e.returncode}, 标准错误输出: {e.stderr}, 标准输出: {e.stdout}")
            return jsonify(
                result="语音转换失败，请更换语音重试。",
                errCode=-1
            )
    else:
        current_app.logger.error("The required file ffmpeg does not exist.")
        return jsonify(
            result="语音转换服务异常",
            errCode=-2
        )
    f = open(result_file_path, 'rb')
    content = f.read()
    f.close()
    if asr_type == "minnanyu":
        url = current_app.config["MINNAN_WAV_ASR_URL"]
    else:  # 普通话
        url = current_app.config["WAV_ASR_URL"]
    try:
        res = requests.post(url=url, data=params, files={'file': content})
        res_json = json.loads(res.text, strict=False)

        # 检查语音识别服务的返回结果
        if 'errCode' in res_json and res_json['errCode'] == '0':
            # 识别成功，返回前端期望的格式
            return jsonify(
                state="Success",
                msg=res_json.get('result', '识别结果为空')
            )
        else:
            # 识别失败
            error_msg = res_json.get('result', '语音识别失败')
            current_app.logger.warn(f"语音识别失败: {res_json}")
            return jsonify(
                state="Error",
                msg=error_msg
            )

    except Exception as e:
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="语音识别服务异常，请重试。"
        )
    finally:
        # PC端测试时暂时先不删除音频
        # os.remove(raw_file_path)
        # os.remove(result_file_path)
        pass


@ajax_bp.route('/voice_detection', methods=['POST'])
def voice_detection():
    """
    语音鉴伪API调用
    调用外部API进行AI生成音频检测
    """
    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify(
                state="Error",
                msg="未找到上传的音频文件"
            )

        file = request.files['file']
        if file.filename == '':
            return jsonify(
                state="Error",
                msg="未选择文件"
            )

        # 检查文件类型
        allowed_extensions = {'.wav', '.mp3', '.m4a'}
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            return jsonify(
                state="Error",
                msg="不支持的文件格式，请上传 WAV、MP3 或 M4A 格式的音频文件"
            )

        # 调用外部API
        api_url = "https://znr6jspt-67s1780s-9397.zjrestapi.gpufree.cn:8443/detect"

        # 准备文件数据
        files = {
            'file': (file.filename, file.stream, file.content_type)
        }

        # 发送请求到外部API
        response = requests.post(api_url, files=files, timeout=30)

        if response.status_code == 200:
            result_data = response.json()
            return jsonify(
                state="Success",
                result=result_data,
                msg="识别完成"
            )
        else:
            current_app.logger.error(f"外部API调用失败: {response.status_code}, {response.text}")
            return jsonify(
                state="Error",
                msg=f"外部API调用失败，状态码: {response.status_code}"
            )

    except requests.exceptions.Timeout:
        current_app.logger.error("外部API调用超时")
        return jsonify(
            state="Error",
            msg="请求超时，请重试"
        )
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"外部API调用异常: {str(e)}")
        return jsonify(
            state="Error",
            msg="网络请求异常，请检查网络连接"
        )
    except Exception as e:
        current_app.logger.error(f"语音鉴伪处理异常: {str(e)}")
        current_app.logger.warn(traceback.format_exc())
        return jsonify(
            state="Error",
            msg="语音鉴伪服务异常，请重试"
        )