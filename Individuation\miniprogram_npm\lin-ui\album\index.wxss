.container{display:grid}.vertical{height:360rpx}.parent{display:inline-grid;grid-template-columns:repeat(auto-fill)}.child{box-sizing:border-box;background-color:#fff;flex:0 0 44%;height:100px;border:1px solid red;margin-top:4%;margin-left:2%;margin-right:2%}.dimback{background:#000}.dim{opacity:.6}.imageContainer{position:relative}.imageContainer:last-child>.text{color:#fff;font-weight:700;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-size:larger}