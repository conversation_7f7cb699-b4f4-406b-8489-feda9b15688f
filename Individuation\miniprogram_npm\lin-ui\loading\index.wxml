<view class="container-loading l-container-class {{fullScreen ? 'content': ''}}" wx:if="{{show && fullScreen}}" style="background:{{bgColor}};opacity:{{opacity}};z-index:{{zIndex}}" catchtouchmove="doNothingMove">
  <view wx:if="{{show}}" class="l-class {{type + '-spinner'}} {{type==='change'||custom?'':'spinner-' + type + '-' + size}}" style="margin-bottom:{{distance}}px">
    <block wx:if="{{custom}}">
      <slot/>
    </block>
    <block wx:else>
      <view wx:if="{{type==='flash' || type==='change' || type==='flip'}}" style="{{color?'background-color:'+color:''}}" class="{{type+'-bounce1'}} {{'spinner-'+ type + '-' + size}}"></view>
      <view wx:if="{{type==='flash' || type==='change'}}" style="{{color?'background-color:'+color:''}}" class="{{type+'-bounce2'}} {{'spinner-'+ type + '-' + size}}"></view>
      <view wx:if="{{type==='change'}}" style="{{color?'background-color:'+color:''}}" class="{{type+'-bounce3'}} {{'spinner-'+ type + '-' + size}}"></view>
      <view class="spinner-circle {{'spinner-circle-' + size}}" wx:if="{{ type === 'circle'}}">
        <view class="spinner-container container1">
          <view class="circle1 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle2 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle3 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle4 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
        </view>
        <view class="spinner-container container2">
          <view class="circle1 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle2 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle3 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle4 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
        </view>
        <view class="spinner-container container3">
          <view class="circle1 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle2 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle3 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
          <view class="circle4 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
        </view>
      </view>
      <view wx:if="{{ type === 'rotate'}}">
        <view class="rotate rotate-{{size}}" style="border-color: {{color}};"></view>
      </view>
    </block>
  </view>
</view>
<view wx:if="{{!fullScreen}}" class="l-container-class" style="position: relative">
    <slot name="content"/>
    <view wx:if="{{show}}" class="inner-loading-container" style="background:{{bgColor}};opacity:{{opacity}};z-index:{{zIndex}}"></view>
    <view wx:if="{{show}}" class="l-class loading-icon-container" style="z-index:{{zIndex + 1}}">
        <view class="{{type + '-spinner'}} {{type==='change'||custom?'':'spinner-' + type + '-' + size}}">
            <block wx:if="{{custom}}">
                <slot/>
            </block>
            <block wx:else>
                <view wx:if="{{type==='flash' || type==='change' || type==='flip'}}" style="{{color?'background-color:'+color:''}}" class="{{type+'-bounce1'}} {{'spinner-'+ type + '-' + size}}"></view>
                <view wx:if="{{type==='flash' || type==='change'}}" style="{{color?'background-color:'+color:''}}" class="{{type+'-bounce2'}} {{'spinner-'+ type + '-' + size}}"></view>
                <view wx:if="{{type==='change'}}" style="{{color?'background-color:'+color:''}}" class="{{type+'-bounce3'}} {{'spinner-'+ type + '-' + size}}"></view>
                <view wx:if="{{ type === 'circle'}}" class="spinner-circle {{'spinner-circle-' + size}}">
                    <view class="spinner-container container1">
                        <view class="circle1 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle2 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle3 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle4 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                    </view>
                    <view class="spinner-container container2">
                        <view class="circle1 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle2 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle3 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle4 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                    </view>
                    <view class="spinner-container container3">
                        <view class="circle1 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle2 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle3 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                        <view class="circle4 container-view {{type + '-' + size}}" style="background: {{color}}"></view>
                    </view>
                </view>
                <view wx:if="{{ type === 'rotate'}}">
                    <view class="rotate rotate-{{size}}" style="border-color: {{color}};"></view>
                </view>
            </block>
        </view>
    </view>
</view>

