Component({relations:{"../image-clipper/index":{type:"parent"}},externalClasses:["l-class"],properties:{zIndex:{type:Number,value:999},lockWidth:{type:Boolean,value:!1},lockHeight:{type:<PERSON>olean,value:!1},lockRatio:{type:<PERSON>olean,value:!1},disableScale:{type:Number,value:!1},disableRotate:{type:Number,value:!1},limitMove:{type:Boolean,value:!1}},data:{formColor:"#3963bc",lockWidthValue:!1,lockHeightValue:!1,lockRatioValue:!0,disableScaleValue:!1,disableRotateValue:!1,limitMoveValue:!1},methods:{bindSwitchChange:async function(e){const a=e.detail.value,l=e.currentTarget.dataset.type;let t=this.getRelationNodes("../image-clipper/index")[0];await t.setData({[l]:a})}}});