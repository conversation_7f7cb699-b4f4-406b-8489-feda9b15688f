<!doctype html>
<html lang="zh-CN" style="min-height: 100%;">
<head>
    {% block head %}
        <!-- Required meta tags -->
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Expires" content="0" />
        <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
        {% block styles %}
            <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}" type="text/css">
            <link rel="stylesheet" href="{{ url_for('static', filename='open-iconic/font/css/open-iconic-foundation.css') }}">
            <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}" type="text/css">
        {% endblock styles %}
        <title>{% block title %}中英粤识别测试{% endblock title %}</title>
    {% endblock head %}
</head>

<body>
{% block nav %}
    <div class="navbar-container bg-dark">
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark" id="mainNav">
            <a class="navbar-brand" href="{{ url_for('voice_presentation.index') }}"></a>
            <!-- 当页面宽度减短时，将导航收在图标内 -->
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse navbar-font" id="navbarResponsive">
                <ul class="navbar-nav ml-auto">
                     
                  
                </ul>
            </div>
        </nav>
    </div>
{% endblock nav %}

<main>
    {% for message in get_flashed_messages(with_categories=True) %}
        <div class="alert alert-{{ message[0] }}" role="alert">
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            {{ message[1] }}
        </div>
    {% endfor %}

    <!-- Your page content -->
    {% block content %}
    {% endblock content%}


    {% block footer %}
    {% endblock footer %}
</main>
<div id="toast">
    <button class="btn" id="toast-close-btn" aria-hidden="true" onclick="closeToast()">&times;</button>
    <div id="toast-content">
    </div>
</div>
{% block scripts %}

    <script type="text/javascript" src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <!-- Bootstrap core JavaScript -->
    <script type="text/javascript" src="{{ url_for('static', filename='js/bootstrap.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='js/popper.min.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='js/recorder.wav.min.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='js/recorder.pcm.js') }}"></script>

    <script>
        let flash = null;
        function toast(body, time){
            clearTimeout(flash)
            let $toast = $('#toast');
            let $toast_content = $('#toast-content');
            if (body.length >1 ){
                $.each(body,function(index,value){
                    $toast_content.append("<p>"+body[index]+"</p>")
                });
            }
            else {
                $toast_content.empty();
                $toast_content.append("<p>"+body[0]+"</p>");
            }

            $toast.fadeIn();
            if (time){  // 有传入time值就定时淡化，无time就固定。
                flash = setTimeout(function () {
                    $toast.fadeOut();
                }, time);
            }
        }
        function closeToast(){
            $('#toast').hide()
        }
    </script>
{% endblock scripts %}

</body>
</html>