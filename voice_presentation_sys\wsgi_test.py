from flask_sockets import Sockets
from geventwebsocket import WebSocketError
import websocket
import json
from flask import current_app, session
from voice_presentation_sys.extensions import G_WebSocket_Dict, G_multiprocess_Pool
import traceback

import os
from dotenv import load_dotenv


dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)


from voice_presentation_sys import create_app  # noqa
app = create_app('production')

sockets = Sockets(app)


@sockets.route('/send_binary')
def echo_socket(socket):
    print("in route func")
    # 试一下用多进程
    # 第一次发过来的是 socket_id值 例如 stream_asr
    # js中第一次发送写在了ws.onopen中
    socket_id = socket.receive()
    origin = socket.origin

    # try:
    #     socket_id = session["socket_id"]
    # except Exception as e:
    #     current_app.logger.warn(traceback.format_exc())
    # current_app.logger.info(f"socket里面能拿到socket值吗, {s}")
    # print("G_WebSocket_Dict", G_WebSocket_Dict)
    # print("socket里面能拿到socket_id值吗?", socket_id)
    # first_receive_sig = True
    try:
        try:
            ws = G_WebSocket_Dict[socket_id]  # 这个是api的websocket
        except KeyError:
            socket.send(json.dumps({"state": "Error", "content": "WebSocket does not exist, you should create WebSocket first!"}))
            raise WebSocketError("KeyError")  # 是KeyError 但只有这个错才能让此socket结束?(不确定)
        print("before create process")
        G_multiprocess_Pool.submit(f, (socket, ws))
        print("created process")
        # while not socket.closed:  # socket连了就一直在这里面了 直到socket断掉
        #     message = socket.receive()  # 阻塞
        #     ws.send(message[44:])  # 直接送blob给dpService (去掉44字节wav头)
        #     try:
        #         ret = ws.recv()
        #         ret_json_result = json.loads(ret)
        #         current_app.logger.info(ret_json_result)
        #         if ret_json_result != '':
        #             socket.send(json.dumps({"state": "Success", "content": ret_json_result}))
        #     except websocket.WebSocketTimeoutException as e:
        #         pass  # 这里只能pass忽略
        # # 当客户端主动断开链接时, 不会进入WebSocketError错误, 会执行while之后的逻辑.
        # # 客户端主动断开逻辑
        # raise WebSocketError(f"客户端主动断开连接.{origin}")
    except BaseException as e:  # 可以捕捉到这socket断开抛出的错 可能是WebSocketError和WebSocketConnectionClosedException
        try:
            socket.close()  # 尝试关闭和前端的socket连接
        except:
            pass
        current_app.logger.warn(traceback.format_exc())
        return


def f(*args):
    """
    args[0] enter_ws
    args[1] dest_ws
    """
    print("in sub process")
    print(f"{args[0]}")
    args = args[0]
    enter_ws = args[0]
    dest_ws = args[1]
    # current_app.logger.info("in sub process")
    try:
        while not enter_ws.closed:  # socket连了就一直在这里面了 直到socket断掉
            print("while")
            message = enter_ws.receive()  # 阻塞
            dest_ws.send(message[44:])  # 直接送blob给dpService (去掉44字节wav头)
            try:
                ret = dest_ws.recv()
                ret_json_result = json.loads(ret)
                current_app.logger.info(ret_json_result)
                if ret_json_result != '':
                    enter_ws.send(json.dumps({"state": "Success", "content": ret_json_result}))
            except websocket.WebSocketTimeoutException as e:
                pass  # 这里只能pass忽略
        # 当客户端主动断开链接时, 不会进入WebSocketError错误, 会执行while之后的逻辑.
        # 客户端主动断开逻辑
        raise WebSocketError(f"客户端主动断开连接.{enter_ws.origin}")
    except BaseException as e:  # 可以捕捉到这socket断开抛出的错 可能是WebSocketError和WebSocketConnectionClosedException
        try:
            enter_ws.close()  # 尝试关闭和前端的socket连接
        except:
            pass
        current_app.logger.warn(traceback.format_exc())
        return


# if __name__ == '__main__':
#     from gevent import pywsgi
#     from geventwebsocket.handler import WebSocketHandler
#     server = pywsgi.WSGIServer(('127.0.0.1', 8000), app, handler_class=WebSocketHandler)
#     server.serve_forever()


if __name__ == '__main__':
    app.run()
    app.wsgi_app()
