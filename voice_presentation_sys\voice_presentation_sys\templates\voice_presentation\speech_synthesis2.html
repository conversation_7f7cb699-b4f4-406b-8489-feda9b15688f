{% extends 'base.html' %}

{% block styles %}
    {{ super() }}
{% endblock styles %}

{% block content %}
    <div class="container-fluid container-fluid-padding">
        <div class="row">
            <div class="col content-area-container2">
                <input id="voice-content-id" type="text" class="form-control" placeholder="输入subuser标识，然后根据下面的录音文本进行录音。">
                <p style="margin-top: 2rem">我国东北的小兴安岭，有数不清的树，几百里连成一片，就像绿色的海洋。</p>
                <div class="bottom-container">
                    <!--autoplay表明在音频准备就绪后直接进行播放-->
                    <audio class="hidden" id="audio" autoplay src="#"></audio>
                    <button id="record-button" type="button" class="btn btn-primary float-right">
                        开始录音
                        <span class="fi-microphone"></span>
                    </button>
                    <button hidden id="recording-button" type="button" class="btn btn-primary float-right">
                        停止录音
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="sr-only"></span>
                        </div>
                    </button>
                    <button id="upload-wav-button" type="button" class="btn btn-light upload-btn" data-toggle="modal"
                            data-target="#confirm-upload">
                        上传文件
                        <span class="fi-cloud-upload"></span>
                    </button>
                </div>
            </div>
            <div class="col content-area-container2" style="margin-right: 0;">
                <div class="checkbox">
                    <div class="custom-control custom-checkbox">
                        <input class="custom-control-input" type="radio" name="exampleRadios" id="exampleRadios1" value="春天，树木抽出新的枝条，长出嫩绿的叶子。山顶上的积雪融化了，雪水汇成了小溪。" checked>
                        <label class="custom-control-label" for="exampleRadios1">
                            春天，树木抽出新的枝条，长出嫩绿的叶子。山顶上的积雪融化了，雪水汇成了小溪。
                        </label>
                    </div>
                    <div class="custom-control custom-checkbox">
                        <input class="custom-control-input" type="radio" name="exampleRadios" id="exampleRadios2" value="夏天，树木长得郁郁葱葱，密密层层的叶枝把森林封的严严实实的，挡住了人们的视线，遮住了蓝蓝的天空。">
                        <label class="custom-control-label" for="exampleRadios2">
                            夏天，树木长得郁郁葱葱，密密层层的叶枝把森林封的严严实实的，挡住了人们的视线，遮住了蓝蓝的天空。
                        </label>
                    </div>
                </div>

                <div class="bottom-container">
                    <audio class="hidden" id="audio" autoplay="autoplay" src="" type="audio/wav"></audio>
                    <button id="play-button" type="button" class="btn btn-primary float-right" data-lang="putonghua" data-id="putonghua0">
                        开始播放
                        <span class="fi-caret-right"></span>
                    </button>
                    <button id="loading-button" type="button" class="btn btn-primary float-right" hidden>
                        正在播放
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="sr-only"></span>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- 文件上传模态框 -->
    <div class="modal fade" id="confirm-upload" tabindex="-1" role="dialog" aria-labelledby="文件上传" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="upload-form" enctype="multipart/form-data">
                        <input id="file"  type="file"  name="wav_file" required accept=".wav, .mp3">
                        <input id="submit-file" type="button" aria-hidden="true" data-dismiss="modal"
                               aria-label="Close" class="btn btn-secondary" value="提交">
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div align="center" style="font-size:16px;color:#000"> &nbsp;&nbsp;&nbsp;&nbsp;    联系电话: 0592-5998812 &nbsp;&nbsp;&nbsp;&nbsp;        
     <a href="https://www.talentedsoft.com/" style=" color:#000;">公司网站链接</a> &nbsp;&nbsp;    
    </div>    
{% endblock content %}

{% block scripts %}
    {{ super() }}
    <script type="text/javascript" src="{{ url_for('static', filename='js/speech_synthesis2.js') }}"></script>
{% endblock scripts %}
