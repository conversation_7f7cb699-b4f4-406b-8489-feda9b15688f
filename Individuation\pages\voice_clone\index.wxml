<!-- 语音克隆页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="title">语音克隆</view>
  <view class="subtitle">上传音频样本，克隆您的声音</view>

  <!-- 文本输入区域 -->
  <view class="section">
    <view class="section-title">输入要合成的文本</view>
    <textarea 
      class="text-input" 
      placeholder="请输入要进行语音合成的文本内容..."
      value="{{textContent}}"
      bindinput="onTextInput"
      maxlength="500"
    ></textarea>
    <view class="char-count">{{textContent.length}}/500</view>
  </view>

  <!-- 录音区域 -->
  <view class="section">
    <view class="section-title">录制音频样本</view>
    <view class="record-area">
      <view class="record-tips">请录制5-10秒的清晰语音作为声音样本</view>
      
      <view class="record-controls">
        <button 
          class="record-btn {{isRecording ? 'recording' : ''}}"
          bindtouchstart="startRecord"
          bindtouchend="stopRecord"
          disabled="{{isSynthesizing}}"
        >
          <view class="record-icon">
            <text class="iconfont {{isRecording ? 'icon-stop' : 'icon-mic'}}"></text>
          </view>
          <text class="record-text">{{isRecording ? '松开停止' : '按住录音'}}</text>
        </button>
        
        <button 
          class="play-btn" 
          bindtap="playRecord"
          disabled="{{!recordFilePath || isSynthesizing}}"
        >
          <text class="iconfont icon-play"></text>
          <text>试听录音</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 语音克隆区域 -->
  <view class="section">
    <view class="section-title">语音克隆</view>
    <button 
      class="clone-btn {{isSynthesizing ? 'loading' : ''}}"
      bindtap="startVoiceClone"
      disabled="{{isSynthesizing || !textContent.trim() || !recordFilePath}}"
    >
      <view class="btn-content">
        <text class="iconfont {{isSynthesizing ? 'icon-loading' : 'icon-clone'}}"></text>
        <text>{{isSynthesizing ? '正在克隆...' : '开始克隆'}}</text>
      </view>
    </button>
  </view>

  <!-- 结果播放区域 -->
  <view class="section" wx:if="{{resultAudioUrl}}">
    <view class="section-title">克隆结果</view>
    <view class="result-area">
      <button class="play-result-btn" bindtap="playResult">
        <text class="iconfont icon-play"></text>
        <text>播放克隆语音</text>
      </button>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section">
    <view class="help-title">使用说明</view>
    <view class="help-content">
      <text>1. 输入要合成的文本内容</text>
      <text>2. 按住录音按钮录制5-10秒清晰语音</text>
      <text>3. 点击"开始克隆"进行语音合成</text>
      <text>4. 播放克隆结果，体验您的专属声音</text>
    </view>
  </view>
</view>
