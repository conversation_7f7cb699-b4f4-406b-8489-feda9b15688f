<!-- 正常的训练语音录制，需要录制多条 -->
<view class="container">
    <view class="serial-container">
            <text>{{text_count}}</text>
    </view>
    <view class="text-container">
        <text>{{textcontent}}</text>
    </view>

    <image src="../../images/luyin.gif" bindtap='startRecord' class="center-img" wx-if="{{isluyin}}" />
    <view class="tips">
        <text>语音文件上传进度条</text>
        <l-progress percent="{{ upload_percent }}" show-info="{{true}}"></l-progress>  <!-- 百分比的值是已上传文件和后端最小上传文件阈值的比 -->
    </view>

    <!-- 未录音 -->
    <view class="{{!isluyin?'bofanimgtwo':'bofanimgtwo on'}}" bindtouchstart="startRecord" bindtouchend="stopRecord">
    </view>
    <text class="tip">按住麦克风按钮，然后开始朗读</text>
    <!-- <image src="../../images/tipimg.png" class="tipimg" wx-if="{{isshow}}" /> -->
</view>