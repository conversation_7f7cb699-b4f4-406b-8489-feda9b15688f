.container{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx 50rpx;  /*上下 左右*/
}

.container-top{
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #007fff;
    color: #fff;
    width: 100%;
    padding: 20rpx 0rpx 20rpx 0rpx;  /*上下左右*/
    border-radius:10rpx
}

.step-container{
    display: flex;
    flex-direction: column;
    align-items: center;
}

.step-container-title{
    /* margin-top: 8rpx; */
    color: #007fff;
}

.step-container-content{
    margin-top: 30rpx;
}