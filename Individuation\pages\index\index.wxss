/* pages/welcome/welcome.wxss */
page{  /*单页面page标签背景色设置*/
    background-color: #99ccff;
}
.container{
    display: flex;
    flex-direction: column; /*设置flex容器垂直排列*/
    align-items: center; /*容器内所有子元素居中*/
}
.motto{
    margin-top: 100rpx;
    font-size: 34rpx;
    font-weight: bold;  /*设置字体宽度*/
}
.journey-container{
    border: 1px solid #405f80;
    width: 200rpx;
    height: 80rpx;
    border-radius: 5px;
    text-align: center;
    margin-top: 200rpx;
}
.journey{
    font-size: 23rpx;
    color: #405f80;
    line-height: 80rpx; /*当行高=容器高度时，文本等于垂直居中了*/
    font-weight: bold;
}
