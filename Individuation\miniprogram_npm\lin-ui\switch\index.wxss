.switch{position:relative;display:inline-block;box-sizing:content-box;width:2em;height:1em;background-color:#fff;border:1px solid rgba(0,0,0,.1);border-radius:1em;transition:background-color .3s}.switch-node{position:absolute;top:0;left:0;border-radius:100%;z-index:1;width:1em;height:1em;background-color:#fff;box-shadow:0 3px 1px 0 rgba(0,0,0,.05),0 2px 2px 0 rgba(0,0,0,.1),0 3px 3px 0 rgba(0,0,0,.05);transition:.3s cubic-bezier(.3,1.05,.4,1.05)}.switch-on .switch-node{transform:translateX(calc(1em))}.switch-disabled{opacity:.5}