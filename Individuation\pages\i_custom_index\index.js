// pages/i_custom_index.js
const common = require("../../dist/js/common.js")
const app = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {

    },

    onStartCustom2() {
        wx.navigateTo({
            url: '../i_record2/index',
        })
    },

    onStartCustom() {
        // 创建系统默认名称的模型 /util/create_model
        // 跳转录音页面
        wx.showLoading({
            title: '加载中',
        })
        common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/util/wechat/create_model", {}, {
            token: app.globalData.userInfo.token
        }, "GET").then(res => {
            wx.hideLoading({
                success: (res) => {},
            })
            wx.navigateTo({
                url: '../i_record/index',
            })
        }).catch(err => {
            // 服务异常就不跳转
            wx.hideLoading({
                success: (res) => {},
            })
            wx.showToast({
              title: '服务未响应，功能暂时无法使用',
              duration: 2000,
              icon: "none"
            })
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {
        // 每次启动小程序才判断
        // 是否存在 token 是否过期 否则 获取手机号码 获取token 存储token
        const token = app.globalData.userInfo.token
        if (token === "") {
            common.loginAndToast()
        } else { // 如果有token就 请求验证一下token 如果 401 就自动跳转登录
            common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/util/verify_token", {}, {
                token: token
            })
            app.globalData.userInfo.token = token // 过了验证就设置一下全局的token值
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})