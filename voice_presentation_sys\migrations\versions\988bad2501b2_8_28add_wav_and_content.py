"""8.28add wav_and_content

Revision ID: 988bad2501b2
Revises: 
Create Date: 2020-08-28 11:25:30.686019

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '988bad2501b2'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=20), nullable=True),
    sa.Column('password_hash', sa.String(length=128), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('wav_and_content',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('wav_type', sa.String(length=30), nullable=True),
    sa.Column('wav_name', sa.String(length=30), nullable=True),
    sa.Column('wav_content', sa.String(length=256), nullable=True),
    sa.Column('modi_date', sa.DateTime(), nullable=True, comment='修改时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('photo')
    op.drop_index('ix_module_name', table_name='module')
    op.drop_index('ix_module_name_pinyin', table_name='module')
    op.drop_index('ix_module_type', table_name='module')
    op.drop_table('module')
    op.drop_table('learn_info2')
    op.drop_table('admin')
    op.drop_index('anchor_name', table_name='course')
    op.drop_index('ix_course_name', table_name='course')
    op.drop_table('course')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('course',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=30), nullable=True),
    sa.Column('anchor_name', mysql.VARCHAR(length=20), nullable=True),
    sa.Column('modi_date', mysql.DATETIME(), nullable=True, comment='修改时间'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_course_name', 'course', ['name'], unique=True)
    op.create_index('anchor_name', 'course', ['anchor_name'], unique=True)
    op.create_table('admin',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('username', mysql.VARCHAR(length=20), nullable=True),
    sa.Column('password_hash', mysql.VARCHAR(length=128), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('learn_info2',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('type', mysql.INTEGER(), autoincrement=False, nullable=True, comment='代表不同业务'),
    sa.Column('text', mysql.CHAR(length=128), nullable=True, comment='文本'),
    sa.Column('xm_male', mysql.CHAR(length=128), nullable=True, comment='厦门男声文件url'),
    sa.Column('xm_female', mysql.CHAR(length=128), nullable=True, comment='厦门女声文件url'),
    sa.Column('qz_male', mysql.CHAR(length=128), nullable=True, comment='泉州男声文件url'),
    sa.Column('qz_female', mysql.CHAR(length=128), nullable=True, comment='泉州女声文件url'),
    sa.Column('zz_male', mysql.CHAR(length=128), nullable=True, comment='漳州男声文件url'),
    sa.Column('zz_female', mysql.CHAR(length=128), nullable=True, comment='漳州女声文件url'),
    sa.Column('tw_male', mysql.CHAR(length=128), nullable=True, comment='台湾男声文件url'),
    sa.Column('tw_female', mysql.CHAR(length=128), nullable=True, comment='台湾女声文件url'),
    sa.Column('modi_date', mysql.DATETIME(), nullable=True, comment='修改时间'),
    sa.Column('module_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='learn_info2_ibfk_1'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('module',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=30), nullable=False),
    sa.Column('name_pinyin', mysql.VARCHAR(length=30), nullable=True),
    sa.Column('type', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('photo_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('course_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['course_id'], ['course.id'], name='module_ibfk_2'),
    sa.ForeignKeyConstraint(['photo_id'], ['photo.id'], name='module_ibfk_1'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_module_type', 'module', ['type'], unique=True)
    op.create_index('ix_module_name_pinyin', 'module', ['name_pinyin'], unique=True)
    op.create_index('ix_module_name', 'module', ['name'], unique=True)
    op.create_table('photo',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('filename', mysql.VARCHAR(length=64), nullable=True),
    sa.Column('timestamp', mysql.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.drop_table('wav_and_content')
    op.drop_table('user')
    # ### end Alembic commands ###
