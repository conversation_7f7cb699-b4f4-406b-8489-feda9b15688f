$(function () {
    var upload_wav = false
    var denoised_wav_url = ""


    $('#submit-file').on('click', function () {

        // 获取表单对象
        let fm = $('#upload-form');
        let spin = $("#spinner")
        let formData = new FormData(fm[0]);
        console.log(fm)
        wavesurfer1.loadBlob(fm[0][0].files[0])  // 直接加载File或是Blob对象，console.log进去一层一层找
        upload_wav = true
        formData.append("file_type", "upload_file");  // 因后台使用同一个处理函数，所以此处作为一个标记。
        spin.removeAttr("hidden"); //spinner默认是Hidden的，事件开始需要显示出来。

        $.ajax({
            type: 'POST',
            url: '/ajax/denoise',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (data) {
                spin.attr("hidden", "hidden")  //加载动画隐藏
                if (data.state === 'Success') {
                    denoised_wav_url = "/ajax/denoise/wav?wavfile=" + data.msg
                    wavesurfer2.empty()  // 新上传文件成功后，把之前文件的波形清除
                    toast(['文件提交成功，等待降噪'], 2000)
                    console.log("新的wav_url: ", denoised_wav_url)
                } else {
                    toast([data.msg], 2000);  //toast弹窗显示错误信息
                }
            },
            error: function () {
                spin.attr("hidden", "hidden")  //加载动画隐藏
                toast(['服务异常'], 2000)
            }
        });
    })

    var wavesurfer1 = WaveSurfer.create({
        container: '#waveform1'
    });

    var wavesurfer2 = WaveSurfer.create({
        container: '#waveform2'
    });

    // 回调注册
    wavesurfer1_event_load(wavesurfer1)
    wavesurfer2_event_load(wavesurfer2)


    $('#play-button1').on('click', function (e) {
        if (!upload_wav) {
            toast(["暂无语音可以播放，请上传文件后尝试。"], 1500)
            return
        }
        wavesurfer1.play()
    })

    $('#loading-button1').on('click', function (e) {
        let play_button = $('#play-button1');
        let load_button = $('#loading-button1');

        play_button.attr("hidden", false);
        load_button.attr("hidden", true);

        // 停止播放并回到波形开头
        wavesurfer1.stop()
    })

    $('#play-button2').on('click', function (e) {
        if (denoised_wav_url == "") {
            toast(["暂无语音可以播放，请等待生成或重新上传文件尝试。"], 1500)
            return
        }

        $.ajax({
            type: 'GET',
            url: denoised_wav_url,
            success: function (data) {  // 404和500不会进success
                if (!data.statusCode) {  // 如果拿不到statusCode，那么说明data现在是语音二进制数据
                    wavesurfer2.load(denoised_wav_url)
                }
            },
            error: function (e) {
                // 404
                if (e.status == 404){
                    toast([JSON.parse(e.responseText).msg], 1500)
                    return
                }
                // 500
                toast(['服务异常'], 1500)
            }
        });

    })

    $('#loading-button2').on('click', function (e) {
        let play_button = $('#play-button2');
        let load_button = $('#loading-button2');

        play_button.attr("hidden", false);
        load_button.attr("hidden", true);

        // 停止播放并回到波形开头
        wavesurfer2.stop()
    })

    // 第一个波形图
    function wavesurfer1_event_load(w) {
        let play_button = $('#play-button1');
        let load_button = $('#loading-button1');
        // 播放开始
        w.on('play', function () {
            play_button.attr("hidden", true);
            load_button.attr("hidden", false);
        });
        // 播放结束
        w.on('finish', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);
        });
        // 发生错误
        w.on('error', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);
            console.log("波形图加载异常。")
        });
    }

    // 第二个波形图
    function wavesurfer2_event_load(w) {
        let play_button = $('#play-button2');
        let load_button = $('#loading-button2');
        // 绘制完波形之后
        w.on('ready', function () {
            w.play();
        });
        // 播放开始
        w.on('play', function () {
            play_button.attr("hidden", true);
            load_button.attr("hidden", false);
        });
        // 播放结束
        w.on('finish', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);
        });
        // 发生错误
        w.on('error', function () {
            play_button.attr("hidden", false);
            load_button.attr("hidden", true);
            console.log("波形图加载异常。")
        });
    }
})