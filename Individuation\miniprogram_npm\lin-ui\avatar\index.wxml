
<view class="l-avatar {{text||_isHaveUserNickName?'l-placement-'+placement:''}}" mut-bind:tap="tapAvatar">
    <view class="l-avatar-image {{shape?'l-'+shape:''}} l-class" wx:if="{{_isHaveUserAvatarUrl||icon||src}}" style="width:{{size}}rpx;height:{{size}}rpx;min-width:{{size}}rpx;min-height:{{size}}rpx;">
        <open-data class="open-data" wx:if="{{_isHaveUserAvatarUrl}}" type="userAvatarUrl"/>
        <l-icon wx:elif="{{icon}}" size="{{iconSize || size*0.6}}" color="{{iconColor||'#ffffff'}}" name="{{icon}}"/>
        <image wx:elif="{{src}}" src="{{src}}" mode="{{mode}}" style="width:{{size}}rpx;height:{{size}}rpx"/>
    </view>
    <view class="l-avatar-text l-class-text l-text-class" wx:if="{{text||_isHaveUserNickName}}">
        <open-data class="open-data" wx:if="{{_isHaveUserNickName}}" type="userNickName"/>
        <text class="l-avatar-text-text" wx:elif="{{text}}">{{text}}</text>
    </view>
</view>
