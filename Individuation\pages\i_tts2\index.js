// pages/tts.js
const app = getApp()
var common = require("../../dist/js/common.js")
const util = require("../../utils/util.js")
const {
    individuationTtsRequest,
    handleTtsError
} = util
Page({
    /**
     * 页面的初始数据
     */
    data: {
        //语音播放上下文
        innerAudioContext: {},
        //语音播放状态
        audioStatus: "stop",
        //文本框默认内容
        textcontent: "",
        // textcontent在全局ttsStorys中的索引位置
        contentIndex: 0,
        //语言按键激活标识
        currentActiveName: "",
        //语音模型类别
        currentActiveTag: "train",
        // 检索到的可用模型列表
        usableModels: [],
        // xvector模型
        xvectorModels: [],
        // 进度条的百分比值(整数)
        progressBarPercentage: 0,
    },
    imgerror(event) {
        console.log("detail: ", event.detail)
    },
    // 点击添加模型按钮 则跳转模型定制页面
    onAddNewModel() {
        wx.redirectTo({
            url: '../i_custom_index/index',
        })
    },

    // 更换用于合成语音的故事文本
    switchPreStory() {
        //停止播放
        this.data.innerAudioContext.stop();
        this.setData({
            audioStatus: 'stop'
        })
        //切换文本
        if (this.data.contentIndex == 0) {
            this.setData({
                contentIndex: app.globalData.ttsStorys.length - 1,
                textcontent: app.globalData.ttsStorys[app.globalData.ttsStorys.length - 1].content
            })
        } else {
            this.setData({
                textcontent: app.globalData.ttsStorys[this.data.contentIndex - 1].content,
                contentIndex: this.data.contentIndex - 1
            })
        }
    },
    // 更换用于合成语音的故事文本
    switchNexStory() {
        //停止播放
        this.data.innerAudioContext.stop();
        this.setData({
            audioStatus: 'stop'
        })
        //切换文本
        if (this.data.contentIndex == app.globalData.ttsStorys.length - 1) {
            this.setData({
                textcontent: app.globalData.ttsStorys[0].content,
                contentIndex: 0
            })
        } else {
            this.setData({
                textcontent: app.globalData.ttsStorys[this.data.contentIndex + 1].content,
                contentIndex: this.data.contentIndex + 1
            })
        }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // console.log(options)
        let innerAudioContext = wx.createInnerAudioContext();
        innerAudioContext.useWebAudioImplement = true;
        innerAudioContext.autoplay = true
        const index = parseInt(options.contentindex)
        this.setData({
            innerAudioContext: innerAudioContext,
            currentActiveName: options.currentactivename || this.data.currentActiveName,
            currentActiveTag: options.currentactivetag || this.data.currentActiveTag,
            textcontent: app.globalData.ttsStorys[index].content, // 默认第一个
            contentIndex: index // 当前传入的content内容在全局的ttsStorys中的索引位置
        })
        this.registerInnerAudioContext(); // 注册回调函数
    },

    /*页面切换*/
    onUnload() {
        this.data.innerAudioContext.stop() // 停止播放
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        wx.showLoading({
            title: '加载中'
        })

        common.ajaxCommon("https://tc.talentedsoft.com:58123/individuation/util/wechat/query_model_all", {}, {
            token: app.globalData.userInfo.token
        }, "GET").then(res => {
            if (res.statusCode == 200) {
                if (res.data.state == "success") {
                    this.setData({
                        usableModels: res.data.useable_models,
                        xvectorModels: res.data.xvector_models,
                    })
                    wx.hideLoading({
                        success: (res) => {},
                    })
                } else {
                    wx.showToast({
                        title: "服务异常，查询失败",
                        icon: "none",
                        duration: 2000
                    })
                }
            }
        }).catch(err => {
            wx.showToast({
                title: "请求失败",
                icon: "none",
                duration: 2000
            })
        })
    },

    //按键点击(更改点亮的按钮)
    onChangeActive: function (event) {
        this.setData({
            currentActiveName: event.currentTarget.dataset.name,
            currentActiveTag: event.currentTarget.dataset.tag
        })
        this.data.innerAudioContext.stop();
        //停止播放
        this.setData({
            audioStatus: 'stop'
        })
    },

    //进度条更新定时器函数
    updateProgressBarTimerFunc: function () {
        let innerAudioContext = this.data.innerAudioContext
        if (this.data.audioTimer != null) { // 如果有定时器了就不再创建定时器
            return
        }
        this.data.audioTimer = setInterval(() => {
            const duration = innerAudioContext.duration
            const currentTime = innerAudioContext.currentTime
            console.log("音频总时长: ", duration)
            console.log("当前播放时间: ", currentTime)
            if (duration != NaN && duration > 0) { // 刚开始duration获取不到时是 NaN ，要等音频开始播放或者加载完才有
                const percentage = currentTime / duration * 100
                if (percentage != this.data.progressBarPercentage) { // 防止每次都修改，导致进度条闪烁
                    this.setData({
                        progressBarPercentage: percentage
                    })
                    // console.log("音频总时长: ", duration)
                    // console.log("当前播放时间: ", currentTime)
                }
                // 如果播放时长等于总时长(就是播完了) 清除定时器
                // 如果在播放时切换了模型，清除定时器，且进度条归0
                if (currentTime == duration) { // 真机下 这两个值不会相等，有毒。
                    console.log("清除定时器")
                    this.clearProgressBarTimerFunc()
                }
            }
        }, 200)
    },

    // 清除进度条更新定时器函数
    clearProgressBarTimerFunc: function () {
        this.setData({
            progressBarPercentage: 0
        })
    },

    //在innerAudioContext中填充音频链接
    playStart: function () {
        wx.showLoading({
            title: '加载中',
        })
        const _this = this // 箭头函数里面 用this会有问题
        var textcontent = _this.data.textcontent;

        // 如果没有可用模型
        if (_this.data.usableModels.length == 0 && _this.data.xvectorModels.length == 0) {
            wx.showToast({
                title: '没有模型可用于生成语音',
                duration: 1500,
                icon: 'none'
            })
            return
        }

        // 判断未输入
        if (!textcontent) {
            wx.showToast({
                title: '请输入想要进行语音合成的字段',
                duration: 1500,
                icon: 'none'
            })
            return
        }

        //判断字数上限
        if (textcontent.length > 200) {
            wx.showToast({
                title: '输入的字段不能大于200字',
                duration: 1700,
                icon: 'none'
            })
            return
        }

        if (textcontent.indexOf(" ") >= 0) {
            wx.showToast({
                title: '请勿输入包含空格的字段',
                duration: 1700,
                icon: 'none'
            })
            return
        }

        // 使用统一的个性化TTS请求函数 - 与Flask项目API保持一致
        // 新的API只需要文本内容和subuser参数
        individuationTtsRequest(
            textcontent,
            _this.data.currentActiveName // 使用模型名称作为subuser参数
        ).then(result => {
            wx.hideLoading()

            // 播放音频
            _this.data.innerAudioContext.src = result.audioUrl
            _this.data.innerAudioContext.play()

            //设置全局播放选项
            wx.setInnerAudioOption({
                obeyMuteSwitch: false,
                mixWithOther: false,
            })

        }).catch(error => {
            wx.hideLoading()
            handleTtsError(error, true)
        })

    },
    //按键点击(暂停播放)
    playPaused: function () {
        this.data.innerAudioContext.pause();
    },

    //按键点击(继续播放)
    playGoOn() {
        this.data.innerAudioContext.play();
    },
    //注册音频的状态变化回调函数
    registerInnerAudioContext: function () {
        let innerAudioContext = this.data.innerAudioContext;

        // 监听音频可以播放事件 那就在这里手动play
        innerAudioContext.onCanplay(() => {
            console.log("可以播放")
            // innerAudioContext.play();  // 这个play不放在这里执行，onTimeUpdate就无法触发（????）。
        })

        //监听音频播放事件
        innerAudioContext.onPlay(() => {
            wx.hideLoading({
                success: (res) => {},
            })
            this.setData({
                audioStatus: 'playing',
            })
        })

        //监听音频播放停止事件
        innerAudioContext.onStop(() => {
            console.log('播放停止')
            this.setData({
                audioStatus: 'stop',
            })
        })

        //监听音频自然播放至结束的事件
        innerAudioContext.onEnded((res) => {
            console.log('音频播放结束')
            this.setData({
                audioStatus: 'stop',
                progressBarPercentage: 0
            })
        });

        //监听音频暂停事件
        innerAudioContext.onPause((res) => {
            console.log('播放暂停')
            this.setData({
                audioStatus: 'paused',
            })
        })

        //监听音频播放错误事件
        innerAudioContext.onError((res) => {
            console.log('播放错误', res)
            this.data.innerAudioContext.stop();
            this.setData({
                audioStatus: 'stop',
            })
            console.log("播放重试")
        })

        // 监听currentTime值的变化
        innerAudioContext.onTimeUpdate(() => {
            const duration = innerAudioContext.duration
            const currentTime = innerAudioContext.currentTime
            console.log("音频总时长: ", duration)
            console.log("当前播放时间: ", currentTime)
            const percentage = currentTime / duration * 100
            if (percentage != this.data.progressBarPercentage) { // 防止每次都修改，导致进度条闪烁
                this.setData({
                    progressBarPercentage: percentage
                })
            }
            // onEnded执行之后 这个函数可能还会执行一次 这里防止progressBarPercentage没有清零
            if (duration == currentTime) {
                this.setData({
                    progressBarPercentage: 0
                })
            }
        })
    },
    // 监听音频因为受到系统占用而被中断开始事件。
    onAudioInterruptionBegin: function () {
        this.setData({
            audioStatus: 'stop',
        })
    },
})