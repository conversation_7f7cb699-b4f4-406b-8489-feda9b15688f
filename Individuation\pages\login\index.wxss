/**index.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}

.journey-container{
    border: 1px solid #99ccff;
    width: 200rpx;
    height: 80rpx;
    border-radius: 5px;
    text-align: center;
    margin-top: 200rpx;
}
.journey{
    font-size: 23rpx;
    color: #99ccff;
    line-height: 80rpx; /*当行高=容器高度时，文本等于垂直居中了*/
    font-weight: bold;
}

.motto{
    margin-top: 100rpx;
    font-size: 34rpx;
    font-weight: bold;  /*设置字体宽度*/
}