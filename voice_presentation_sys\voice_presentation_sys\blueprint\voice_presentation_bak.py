# -*- coding: utf-8 -*-
import os

from flask import render_template, Blueprint, send_from_directory, current_app

from voice_presentation_sys.settings import basedir
from voice_presentation_sys.forms import UploadWavForm


voice_presentation_bp = Blueprint('voice_presentation', __name__)


@voice_presentation_bp.route('/', methods=['GET'])
def index():
    """
    主页
    """
    return render_template('voice_presentation/index.html')


@voice_presentation_bp.route('/speech_synthesis', methods=['GET', 'POST'])
def speech_synthesis():
    '''
    语音合成
    :param location: 用来辨别当前页面顶部 哪个跳转点状态应为active
    '''
    return render_template('voice_presentation/speech_synthesis.html', location='speech_synthesis')


@voice_presentation_bp.route('/speech_synthesis2', methods=['GET', 'POST'])
def speech_synthesis2():
    '''
    音色定制
    :param location: 用来辨别当前页面顶部 哪个跳转点状态应为active
    '''
    return render_template('voice_presentation/speech_synthesis2.html', location='speech_synthesis2')


@voice_presentation_bp.route('/speech_denoise', methods=['GET', 'POST'])
def speech_denoise():
    '''
    语音降噪
    '''
    return render_template('voice_presentation/speech_denoise.html', location='speech_denoise')


@voice_presentation_bp.route('/speech_stream_recognition', methods=['GET', 'POST'])
def speech_stream_recognition():
    '''
    中文16k语音流识别
    '''
    return render_template('voice_presentation/speech_stream_recognition.html', location='speech_stream_recognition')


@voice_presentation_bp.route('/speech_stream_recognition_mny', methods=['GET', 'POST'])
def speech_stream_recognition_mny():
    '''
    闽南语16k语音流识别
    '''
    return render_template('voice_presentation/speech_stream_recognition_mny.html', location='speech_stream_recognition_mny')

@voice_presentation_bp.route('/speech_stream_recognition_role', methods=['GET', 'POST'])
def speech_stream_recognition_role():
    '''
    16k语音流识别+role分离
    '''
    return render_template('voice_presentation/speech_stream_recognition_role.html', location='speech_stream_recognition_role')

# @voice_presentation_bp.route('/speech_stream_awake', methods=['GET', 'POST'])
# def speech_stream_awake():
#     '''
#     语音流唤醒
#     '''
#     return render_template('voice_presentation/speech_stream_awake.html', location='speech_stream_awake')


@voice_presentation_bp.route('/speech_recognition', methods=['GET', 'POST'])
def speech_recognition():
    """
    语音识别
    """
    upload_wav_form = UploadWavForm()
    return render_template('voice_presentation/speech_recognition.html', upload_wav_form=upload_wav_form, location='speech_recognition')

@voice_presentation_bp.route('/speech_recognitionfile', methods=['GET', 'POST'])
def speech_recognitionfile():
    """
    语音识别
    """
    upload_wav_form = UploadWavForm()
    return render_template('voice_presentation/speech_recognitionfile.html', upload_wav_form=upload_wav_form, location='speech_recognitionfile')
@voice_presentation_bp.route('/speech_recognitionfile60s', methods=['GET', 'POST'])
def speech_recognitionfile60s():
    """
    语音识别
    """
    upload_wav_form = UploadWavForm()
    return render_template('voice_presentation/speech_recognitionfile60s.html', upload_wav_form=upload_wav_form, location='speech_recognitionfile60s')


@voice_presentation_bp.route('/voiceprint_recognition', methods=['GET', 'POST'])
def voiceprint_recognition():
    """
    声纹识别
    """
    upload_wav_form = UploadWavForm()
    return render_template('voice_presentation/voiceprint_recognition.html', upload_wav_form=upload_wav_form, location='voiceprint_recognition')


@voice_presentation_bp.route('/download/声纹识别_v2.0.0_2020.apk', methods=['GET'])
def get_voiceprint_recognition_apk():
    """
    提供声纹识别apk的下载链接
    """
    return send_from_directory(os.path.join(basedir, 'uploads'), '声纹识别_v2.0.0_2020.apk', as_attachment=True)


@voice_presentation_bp.route('/download/tsasr.apk', methods=['GET'])
def get_speech_recognition_apk():
    """
    提供语音识别apk的下载链接
    """
    return send_from_directory(os.path.join(basedir, 'uploads'), 'tsasr.apk', as_attachment=True)


@voice_presentation_bp.route('/download/minnanyu_Android.apk', methods=['GET'])
def get_minnanyu_speech_recognition_apk():
    """
    提供闽南语语音识别apk的下载链接
    """
    return send_from_directory(os.path.join(basedir, 'uploads'), 'minnanyu_Android.apk', as_attachment=True)



@voice_presentation_bp.route('/voice_clone', methods=['GET','POST'])
def voice_clone():
    """
    音色复刻
    """
    return render_template('voice_presentation/voice_clone.html', location='voice_clone')
