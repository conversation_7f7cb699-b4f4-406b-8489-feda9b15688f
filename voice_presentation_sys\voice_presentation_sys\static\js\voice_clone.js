// voice_clone.js
// 前端 JS
// 控制上传/识别/合成/播放逻辑

$(function () {
    var recording_click_sign = 0; // 用于标识是否重复点击录音按钮
    var recorder;
    let currentAudioBlob = null;
    let lastUploadedFilePath = null;  // ⬅️ 保存上一次服务器上的文件路径

    // 删除函数
    function deleteLastUploadedFile(callback) {
        if (!lastUploadedFilePath) {
            callback();  // 无需删除
            return;
        }
    
        $.ajax({
            url: '/ajax/delete_file',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ path: lastUploadedFilePath }),
            success: function (resp) {
                if (resp.code === 0) {
                    console.log("旧文件删除成功");
                } else {
                    console.warn("旧文件删除失败：" + resp.msg);
                }
            },
            error: function () {
                console.warn("删除请求失败");
            },
            complete: function () {
                lastUploadedFilePath = null;
                callback();  // 无论成功失败都继续执行上传
            }
        });
    }    

    // 录音按钮点击事件
    $('#record-button').on('click', function () {
        if (recorder == null) {
            recorder = Recorder({
                type: 'wav',
                bitRate: 16,        // 采样位数，支持 8 或 16，默认是16
                sampleRate: 16000,  // 采样率，支持 11025、16000、22050、24000、44100、48000
                onProcess: function (buffers, powerLevel, bufferDuration, bufferSampleRate) {
                    // 处理录音数据
                }
            })
        }

        var t = setTimeout(function () {
            console.log("无法录音：权限请求被忽略!");
        }, 8000);

        recorder.open(function () { // 打开麦克风授权获得相关资源
            clearTimeout(t);
            recorder.start(); // 开始录音
            $('#recording-button').attr("hidden", false);  // 显示停止录音按钮
            $('#record-button').attr("hidden", true);  // 隐藏开始录音按钮
        }, function (msg, isUserNotAllow) {
            clearTimeout(t);
            console.log((isUserNotAllow ? "UserNotAllow，" : "") + "无法录音:" + msg);
        });
    })

    // 停止录音按钮点击事件
    $('#recording-button').on('click', function () {
        if (recording_click_sign === 1) {  // 如果当前事件未结束时又被触发点击事件，则退出事件
            return toast(['请不要重复点击停止按钮'], 1500);
        }

        recording_click_sign = 1; // 第一次开始本事件后，置为1，表示事件已被触发，再次点击将不再触发。(事件结尾重置)

        recorder.stop(function (blob, duration) {
            $('#recording-button').attr("hidden", true);  // 隐藏停止录音按钮
            $('#record-button').attr("hidden", false);  // 显示开始录音按钮
            
            // 播放器预览
            const audioUrl = URL.createObjectURL(blob);
            const audioPlayer = $('#audio-player')[0];
            audioPlayer.src = audioUrl;
            audioPlayer.load();

            deleteLastUploadedFile(() => {
                // 上传文件到服务器保存为临时文件
                const formData = new FormData();
                formData.append('wav_blob', blob, `recorded_${Date.now()}.wav`);
                formData.append('file_type', 'record_file');
            
                $.ajax({
                    url: '/ajax/upload',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (resp) {
                        if (resp.code === 0) {
                            currentAudioBlob = blob;
                            lastUploadedFilePath = resp.path;
                        } else {
                            alert("录音上传失败：" + resp.msg);
                        }
                    },
                    error: function () {
                        alert("网络错误，上传失败！");
                    }
                });
            });
        });
        recorder.close(); // 关闭录音器实例
        recording_click_sign = 0; // 重置标志位
    })

    // 提交按钮点击事件
    $('#submit-file').on('click', function () {
        const fileInput = $('#file')[0];
        const file = fileInput.files[0];
    
        if (!file) {
            alert("请选择一个音频文件！");
            return;
        }
    
        // 预览播放器播放选中的音频
        const audioUrl = URL.createObjectURL(file);
        const audioPlayer = $('#audio-player')[0];
        audioPlayer.src = audioUrl;
        audioPlayer.load();
        
        deleteLastUploadedFile(() => {
            // 上传文件到服务器保存为临时文件
            const formData = new FormData();
            formData.append('wav_file', file);
            formData.append('file_type', 'upload_file');  // 标记上传文件类型
        
            $.ajax({
                url: "/ajax/upload",
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (resp) {
                    if (resp.code === 0) {
                        currentAudioBlob = file;
                        lastUploadedFilePath = resp.path;  // 更新最新上传路径
                    } else {
                        alert("上传失败：" + resp.msg);
                    }
                },
                error: function () {
                    alert("上传失败，请检查网络或服务器状态！");
                }
            });
        })     
    });
    
    // 语音合成按钮点击事件
    $('#tts-generate').on('click', function () {
        const text = $('#tts-content').val().trim();
        if (!text) {
            alert("请输入要合成的文本！");
            return;
        }
        if (!currentAudioBlob) {
            alert("请先上传音频文件或录制音频！");
            return;
        }

        // 显示加载中状态
        const button = $(this);
        const spinner = button.find('.spinner-border');
        const btnText = button.find('.btn-text');
        button.prop('disabled', true);
        spinner.removeAttr('hidden');
        btnText.text('合成中...');

        const formData = new FormData()
        formData.append('text', text);
        formData.append('wav_file', currentAudioBlob, 'prompt.wav');

        // 发送合成请求到服务器
        $.ajax({
            url: '/ajax/voice_clone',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhrFields: { responseType: 'blob' }, 
            success: function (blob) {
                // 播放合成后的音频
                const audioUrl = URL.createObjectURL(blob);
                const audioPlayer = $('#tts-audio')[0];
                audioPlayer.src = audioUrl;
                audioPlayer.load();
                audioPlayer.play();
                // 显示播放器区域
                $('#tts-result').prop('hidden', false);
                // 设置下载链接
                $('#tts-download').attr('href', audioUrl);
                $('#tts-download').attr('download', 'tts_output_' + Date.now() + '.wav');              
            },
            error: function (xhr) {
                alert("合成请求失败，请检查网络或服务器状态！");
            },
            complete: function () {
                // ✅ 无论成功或失败都还原按钮状态
                button.prop('disabled', false);
                spinner.attr('hidden', true);
                btnText.text('开始合成');
            }
        });
    })
})