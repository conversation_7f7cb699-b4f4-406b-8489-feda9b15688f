{% extends 'base.html' %}

{% block content %}
    <div class="container-fluid main-container">
        <div class="row">
            <div class="col-sm-2 nav-frame">
                <ul class="nav nav-pills" role="tablist">
                    <li class="nav-item nav-li-container" id="type">
                        <p class="topic">声纹登记（类别）</p>
                        <a class="nav-link btn btn-outline-primary tab-top type-tab active" data-toggle="tab" href="#box" role="tab" data-type="text-independent">
                            文本无关
                        </a>
                        <a class="nav-link btn btn-outline-primary tab type-tab" data-toggle="tab" href="#box" role="tab" data-type="text-related">
                            文本相关
                        </a>
                        <a class="nav-link btn btn-outline-primary tab type-tab" data-toggle="tab" href="#box" role="tab" data-type="dynamic-password">
                            动态口令
                        </a>
                    </li>
                    <li class="nav-item nav-li-container" id="operation" style="margin-top: 20px">
                        <p class="topic">通用操作</p>
                        <a class="nav-link btn btn-outline-primary tab-top operation-tab" data-toggle="tab" href="#box" role="tab" data-operation="voiceprint-confirm">
                            声纹确认
                        </a>
                        <a class="nav-link btn btn-outline-primary tab operation-tab" data-toggle="tab" href="#box" role="tab" data-operation="voiceprint-recognition">
                            声纹辨认
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col content-area-container">
                <div id="top-container">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text" id="basic-addon">用户ID</span>
                        </div>
                        <input id="userid" type="text" class="form-control" placeholder="在此输入声纹ID" aria-label="userid">
                    </div>
                </div>
                <textarea class="border rounded voice-content-area" id="voice-content" maxlength="200" readonly onchange="text_detection(this.value)" placeholder='请先输入您要登记的声纹ID，仅支持英文和数字。输入ID后，参考提示内容，点击 "开始录音" 按钮，倒计时内进行录音，有效内容应不少于10秒。'></textarea>
                <div class="bottom-container">
                    <div class="float-left">
                        <!--autoplay表明在音频准备就绪后直接进行播放-->
                        <audio class="hidden" id="audio" autoplay="autoplay" src="" type="audio/wav"></audio>
                        还可以输入
                        <a id="word-nums" style="color: #fd7e14;">200</a>
                        字
                    </div>
                    <button id="voiceprint-record-button" type="button" class="btn btn-primary float-right" data-type="text-independent" data-operation="null">
                        开始录音
                        <span class="fi-microphone"></span>
                    </button>
                    <button hidden id="voiceprint-recording-button" type="button" class="btn btn-primary float-right">
                        停止录音
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="sr-only"></span>
                        </div>
                    </button>
{#                    <button hidden id="upload-wav-button" type="button" class="btn btn-light float-right" data-toggle="modal"#}
{#                            data-target="#confirm-upload" style="margin-right: 10px;">#}
{#                        上传文件#}
{#                        <span class="fi-cloud-upload"></span>#}
{#                    </button>#}
                </div>
                <!-- 文件上传模态框 -->
{#                <div class="modal fade" id="confirm-upload" tabindex="-1" role="dialog" aria-labelledby="文件上传" aria-hidden="true">#}
{#                    <div class="modal-dialog modal-dialog-centered" role="document">#}
{#                        <div class="modal-content">#}
{#                            <div class="modal-header">#}
{#                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">#}
{#                                    <span aria-hidden="true">&times;</span>#}
{#                                </button>#}
{#                            </div>#}
{#                            <div class="modal-body">#}
{#                                <!--此处附带next值，redirect_back-->#}
{#                                <form id="upload-form" enctype="multipart/form-data">#}
{#                                    <input id="file"  type="file"  name="wav_file" required accept=".wav">#}
{#                                    <input id="voiceprint-submit-file" type="button" aria-hidden="true" data-dismiss="modal"#}
{#                                           aria-label="Close" class="btn btn-secondary" value="提交">#}
{#                                </form>#}
{#                            </div>#}
{#                        </div>#}
{#                    </div>#}
{#                </div>#}
            </div>
        </div>
    </div>
    <div align="center" style="font-size:16px;color:#000"> &nbsp;&nbsp;&nbsp;&nbsp;    联系电话: 0592-5998812 &nbsp;&nbsp;&nbsp;&nbsp;        
     <a href="https://www.talentedsoft.com/" style=" color:#000;">公司网站链接</a> &nbsp;&nbsp;    
{% endblock content %}

{% block scripts %}
    {{ super() }}
    <script type="text/javascript" src="{{ url_for('static', filename='js/voiceprint_recognition_js.js') }}"></script>
    <script>
        let illegality = 0; //文本框输入是否合法标志位
        function text_detection(){
            illegality = 0;  //函数头重置标识位
            let voice_content = $('#voice-content');
            let pattern1 = new RegExp("[A-Za-z]+"); //验证英文
            let pattern2 = new RegExp("[0-9]+");  //验证数字

            $.each(voice_content.val().split(''), function (){

                if (pattern1.test(this)){
                    return true // each函数里面的return true 大约等于 continue
                }
                if (pattern2.test(this)){
                    return true
                }
                else {
                    illegality += 1;
                }
            })
            if (illegality > 0){
                return false
            }
            return true
        }
        toast(['语音识别是一门交叉学科。近二十年来，语音识别技术取得显著进步，开始从实验室走向市场。人们预计，未来10年内，语音识别技术将进入工业、家电、通信、汽车电子、医疗、家庭服务、消费电子产品等各个领域。'])
    </script>
{% endblock scripts %}
