<!--index.wxml-->
<view class="container">
    <!-- <view bind:tap="bindGetMobile" class="journey-container">
        <text class="journey">手机登录2</text>
    </view> -->
    <view style="margin-top: 60rpx;">
        <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">使用手机号码登录</button>
    </view>
    <!-- <text> {{ getPhoneNumberError }} </text> -->
    <!-- <block wx:if="{{hasUserInfo}}">
      <view class="userinfo-avatar" bindtap="bindViewTap">
        <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      </view>
      <text class="userinfo-nickname">{{userInfo.nickName}}</text>
    </block>
    <block wx:elif="{{!hasUserInfo}}">
      <button wx:if="{{canIUseGetUserProfile}}" bindtap="getUserProfile"> 获取头像昵称 </button>
      <button wx:elif="{{canIUse}}" open-type="getUserInfo" bindgetuserinfo="getUserInfo"> 获取头像昵称 </button>
      <view wx:else> 请更新微信 </view>
    </block>
  </view>
  <block wx:if="{{hasUserInfo}}">  <!-- token未过期 或 获取了新token 才显示进入应用界面-->
</view>
