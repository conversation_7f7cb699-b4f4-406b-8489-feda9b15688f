/**index.wxss**/
page {
  background: #fff;
  font-size: 28rpx;
}

.container {
  height: 100%;
  padding: 30rpx 30rpx 0rpx 30rpx;
}

/* 文本输入区域 */
.text-container {
  display: flex;
  align-items: center;
  padding: 0 30rpx 0 30rpx;
  background-color: #e5f2ff;
  height: 400rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

.text-input {
  width: 100%;
  height: 100%;
  font-size: 28rpx;
  line-height: 1.6;
}

/* 区域标题样式 */
.section-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 音色选择区域 */
.voice-section {
  margin-bottom: 30rpx;
}

.voice-list {
  white-space: nowrap;
  height: 120rpx;
}

.voice-item {
  display: inline-block;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx solid #e9ecef;
}

.voice-active {
  background-color: #007fff;
  border-color: #007fff;
}

.voice-name {
  font-size: 28rpx;
  color: #495057;
}

.voice-active .voice-name {
  color: white;
}

/* 语言选择区域 */
.language-section {
  margin-bottom: 40rpx;
}

.language-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.language-item {
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx solid #e9ecef;
  flex: 0 0 auto;
}

.language-active {
  background-color: #007fff;
  border-color: #007fff;
}

.language-name {
  font-size: 28rpx;
  color: #495057;
}

.language-active .language-name {
  color: white;
}

/* 播放控制区域 */
.play-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.play-button {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 20rpx;
}

.play-tip {
  font-size: 28rpx;
  color: rgba(152, 142, 153, 0.9);
  text-align: center;
}

