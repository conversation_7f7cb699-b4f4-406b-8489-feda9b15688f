
$(function (){
    // 以下为voiceprint_recognition.html 所属
    //button active listen methods

    var g_data_type = "text-independent"   //初始值默认文本无关
    $('.operation-tab').on('click', function () {
        let record_btn = $('#voiceprint-record-button');
        record_btn.attr('data-operation', $(this).attr("data-operation"));

        let current_data_type = null

        if (g_data_type != null){
            current_data_type = g_data_type
        }
        else {
            $('.type-tab').each(function () {  //找到当前状态为active的声纹登记类别并赋值给voiceprint-record-button
                if ($(this).hasClass("active")) {
                    current_data_type = $(this).attr("data-type");
                }
            });
        }

        record_btn.attr('data-type', current_data_type);

        let current_operation_tab = $(this);
        $('.operation-tab').each(function () {
            if ($(this) != current_operation_tab) {
                $(this).removeClass("active");
            }
        });

        $('#toast').hide();  //此处防止下面if判断产生的toast块粘滞在各个页面上，点击时直接隐藏。
        $('#toast-content').empty();  //先清空div内的p元素(如果有的话)

        let voice_content = $('#voice-content');
        if (record_btn.attr('data-operation') == "voiceprint-confirm"){
            voice_content.val('');  //先把框内的内容清空，否则placeholder无法显示.
            if (record_btn.attr('data-type') == 'text-independent'){
                toast(['语音识别是一门交叉学科。近二十年来，语音识别技术取得显著进步，开始从实验室走向市场。']);
                voice_content.attr('placeholder', '当前类别: 文本无关\t\n' +
                    '请输入您要确认的声纹ID，输入ID后，点击 "开始录音" 按钮，参考提示内容，在倒计时内进行录音，有效语音不得少于5秒。');
            }
            if (record_btn.attr('data-type') == 'text-related'){
                voice_content.attr('placeholder', '当前类别: 文本相关\t\n' +
                    '请输入您要确认的声纹ID，输入ID后，点击 "开始录音" 按钮，可获取提示，录制内容与您在声纹登记时的录音内容相同。');
            }
            if (record_btn.attr('data-type') == 'dynamic-password'){
                voice_content.attr('placeholder', '当前类别: 动态口令\t\n' +
                    '请输入您要确认的声纹ID，输入ID后，点击 "开始录音" 按钮，可获取提示，录制内容为您在声纹登记时录制的任一1组8位的数字文本。');
            }
        }

        if (record_btn.attr('data-operation') == "voiceprint-recognition"){
            voice_content.val('');  //先把框内的内容清空，否则placeholder无法显示.
            if (record_btn.attr('data-type') == 'text-independent'){
                toast(['语音识别是一门交叉学科。近二十年来，语音识别技术取得显著进步，开始从实验室走向市场。']);
                voice_content.attr('placeholder', '当前类别: 文本无关\t\n' +
                    '请录制任意内容的不少于5秒的语音');
            }
            if (record_btn.attr('data-type') == 'text-related'){
                voice_content.attr('placeholder', '当前类别: 文本相关\t\n' +
                    '请录制您在声纹登记时录制的文本内容,输入正确ID,在点击开始录音后，可获取提示。');
            }
            if (record_btn.attr('data-type') == 'dynamic-password'){
                voice_content.attr('placeholder', '当前类别: 动态口令\t\n' +
                    '请录制您在声纹登记时录制的文本内容,输入正确ID,在点击开始录音后，可获取提示。');
            }
        }
    });

    $('.type-tab').on('click', function () {
        let record_btn = $('#voiceprint-record-button');
        record_btn.attr('data-operation', "null");  // 点击声纹登记任意一个按钮时，将operation置为空

        record_btn.attr('data-type', $(this).attr("data-type"));
        g_data_type = $(this).attr("data-type");  // 这里全局type标记

        let current_tab = $(this);
        $('.type-tab').each(function () {
            if ($(this) != current_tab) {
                $(this).removeClass("active");
            }
        });

        $('.operation-tab').each(function () {
            $(this).removeClass("active");
        });

        $('#toast').hide();  //先隐藏
        $('#toast-content').empty();  //再清空元素

        let voice_content = $('#voice-content');
        if (record_btn.attr('data-type') == "text-independent"){
            //设置输入框状态
            voice_content.val('');  //先把框内的内容清空，否则placeholder无法显示.
            toast(['语音识别是一门交叉学科。近二十年来，语音识别技术取得显著进步，开始从实验室走向市场。人们预计，未来10年内，语音识别技术将进入工业、家电、通信、汽车电子、医疗、家庭服务、消费电子产品等各个领域。'])  // 进入默认显示
            voice_content.attr('placeholder', '请先输入您要登记的声纹ID，仅支持英文和数字。输入ID后，参考提示内容，点击 "开始录音" 按钮，倒计时内进行录音，有效内容应不少于10秒。');

        }
        if (record_btn.attr('data-type') == "text-related"){
            voice_content.val('');  //先把框内的内容清空，否则placeholder无法显示.
            toast(['智 能 软 件', '智 能 软 件', '智 能 软 件']);
            voice_content.attr('placeholder', '请先输入您要登记的声纹ID，仅支持英文和数字。输入ID后，请语音录制如下3组文本,当进行"文本相关"的声纹确认或辨认时，录音一组即可。');
        }
        if (record_btn.attr('data-type') == "dynamic-password"){
            voice_content.val('');  //先把框内的内容清空，否则placeholder无法显示.
            toast(['6703  2589', '7845 6320', '5296 4073', '2647 5938', '9574 0683']);
            voice_content.attr('placeholder', '请先输入您要登记的声纹ID，仅支持英文和数字。输入ID后，请语音录制如下5组8位的数字文本,当进行"动态口令"的声纹确认或辨认时，\t\n任意挑选一组进行录音即可。');
        }
    });

    // $('#voiceprint-submit-file').on('click', function () {
    //     // 获取表单对象
    //     let fm = $('#upload-form');
    //     let voiceprint_record_button = $('#voiceprint-record-button')
    //     let voiceprint_recording_button = $('#voiceprint-recording-button')
    //     let voice_content = $('#voice-content')
    //     let formData = new FormData(fm[0]);
    //     formData.append("file_type", "upload_file");  // 因后台使用同一个处理函数，所以此处作为一个标记。
    //     formData.append("operation_type", voiceprint_record_button.attr('data-type'));
    //     formData.append("operation", voiceprint_recording_button.attr('data-operation'));
    //     formData.append("userid", voice_content.val());
    //
    //     $.ajax({
    //         type: 'POST',
    //         url: '/ajax/voiceprint',
    //         data: formData,
    //         cache: false,
    //         contentType: false,
    //         processData: false,
    //         success: function (data) {
    //             if (data.msg == 'success') {
    //                 voice_content.val(data.msg); //在文本框内填充返回的文本(此处后期修改对象)
    //             } else {
    //                 voice_content.val(data.msg);
    //             }
    //         },
    //     });
    // });

    var time = null  //用来控制voice-record-button的最低录音时长
    function global_time_init(){
        let voiceprint_record_button = $('#voiceprint-record-button')
        if (voiceprint_record_button.attr('data-type') == "text-independent"){
            time = 10000;
        }
        if (voiceprint_record_button.attr('data-operation') == "voiceprint-confirm"){
            if (voiceprint_record_button.attr('data-type') == 'text-independent'){
                time = 5000;  // 设置最低录音时长为5s，此为全局变量
            }
        }
        if (voiceprint_record_button.attr('data-operation') == "voiceprint-recognition"){
            if (voiceprint_record_button.attr('data-type') == "text-independent") {
                time = 5000;
            }
        }
    }

    function detect_user_register_word(userid, token){
        let sign = null
        $.ajax({
            type: 'GET',
            async: false,
            url: '/ajax/detect_user_register_word/'+ userid + '/' + token,
            success: function (data) {
                if (data.state == 'Success') {
                    sign = data.msg
                }
                else {
                    sign = false
                }
            },
        });
        return sign;
    }

    function detect_user_register(userid, token){
        let sign = null
        $.ajax({
            type: 'GET',
            async: false,
            url: '/ajax/detect_user_register/' + userid + '/' + token,
            success: function (data) {
                if (data.state == 'Success') {
                    sign = true;
                } else {
                    sign = false;
                }
            },
        });
        return sign;
    }

    var voiceprint_recording_click_sign = 0;  //0代表recording_button未被点击，1代表已被点击，每次recording-button点击事件完毕重置
    var recorder = Recorder({
        type: "wav",
        bitRate: 16,                 // 采样位数，支持 8 或 16，默认是16
        sampleRate: 16000,              // 采样率，支持 11025、16000、22050、24000、44100、48000
        onProcess:function(buffers,powerLevel,bufferDuration,bufferSampleRate){
        }
    });

    $('#voiceprint-record-button').on('click', function () {
        let voiceprint_record_btn = $('#voiceprint-record-button');
        let voiceprint_recording_btn = $('#voiceprint-recording-button');
        let userid = $('#userid');

        console.log(voiceprint_record_btn.attr('data-type'), voiceprint_record_btn.attr('data-operation'))
        // 如果输入的是除了文本无关的声纹识别之外的操作时，需要检测userid合法性(英文+数字)。
        if (voiceprint_record_btn.attr('data-type') != 'data-independent'){
            // if (voiceprint_record_btn.attr('data-operation') != 'voiceprint-recognition'){
                if (userid.val() != ""){  //通过text检测就往下走，检测注册。
                    //if (!text_detection(userid.val())){ //yhy2024-05-29
                     //   toast(["输入的ID不合法，请重新输入。"+voiceprint_record_btn.attr('data-type')+"--"+userid.val()], 1500);
                     ////   return
                   // }
                }
                else {
                    toast(["请输入ID之后，再使用此功能。"], 1200);
                    return
                }
            // }
        }

        // userid合法性检测2，ajax后台检索数据库查看是否已经注册。
        if (voiceprint_record_btn.attr('data-operation') == 'null') {  // 此处在声纹登记时才验证。
            if (detect_user_register(userid.val(), voiceprint_record_btn.attr('data-type'))) {
            } else {
                toast(["此ID已注册，请重新输入。"], 1300);
                return
            }
        }

        if (voiceprint_record_btn.attr('data-operation') != "null") {
            if (voiceprint_record_btn.attr('data-type') == "text-independent"){
                if (!detect_user_register(userid.val(), voiceprint_record_btn.attr('data-type'))) {
                } else {
                    toast(["此ID未注册，请注册后再使用此功能。"], 1300);
                    return
                }
            }
        }

        // 查询user注册时使用的文本or口令。(除了文本无关类别下的操作)
        if (voiceprint_record_btn.attr('data-operation') != "null") {
            if (voiceprint_record_btn.attr('data-type') != "text-independent") {
                if (userid.val() != "") {
                    let msg = detect_user_register_word(userid.val(), voiceprint_record_btn.attr('data-type'));  //此函数后台查询口令
                    if (msg){
                        toast([msg]);
                    }
                    else{
                        toast(["当前用户未注册，请先注册再使用此功能。"], 1300);
                        return
                    }
                }
            }
        }

        if (recorder == null) {
            recorder = Recorder({
                type: "wav",
                bitRate: 16,                 // 采样位数，支持 8 或 16，默认是16
                sampleRate: 16000,              // 采样率，支持 11025、16000、22050、24000、44100、48000
                onProcess:function(buffers,powerLevel,bufferDuration,bufferSampleRate){
                }
            });
        }
        var t = setTimeout(function(){
            console.log("无法录音：权限请求被忽略!");
        },8000);
        recorder.open(function(){//打开麦克风授权获得相关资源
            clearTimeout(t);
            recorder.start();//开始录音
            voiceprint_record_btn.attr("hidden", true);
            voiceprint_recording_btn.attr("hidden", false);
        },function(msg,isUserNotAllow){
            clearTimeout(t);
            console.log((isUserNotAllow?"UserNotAllow，":"")+"无法录音:"+msg);
        });

        global_time_init();  //这里进行最低语音限制标识time的设置

        // 在最低录音时长完毕后，此停止按钮才可点击
        if (time){
            voiceprint_recording_btn.attr("disabled", true);
            setTimeout(function (){
                voiceprint_recording_btn.removeAttr("disabled", false);
            },time);
        }
    });

    $('#voiceprint-recording-button').on('click', function () {
        let voiceprint_record_btn= $('#voiceprint-record-button')
        let voiceprint_recording_btn= $('#voiceprint-recording-button')
        let voice_content = $('#voice-content')
        let userid = $('#userid')
        if (voiceprint_recording_click_sign === 1) {  //如果当前事件未结束时又被触发点击事件则退出事件
            toast(['请不要重复点击停止按钮'], 1000);
            return
        }
        recorder.stop(function(wav_blob, duration){
            voiceprint_recording_click_sign = 1 // (全局参数)第一次开始本事件后，置为1，表示事件已被触发，再次点击将不再触发。(事件结尾重置)

            voiceprint_recording_btn.attr("hidden", true);
            voiceprint_record_btn.attr("hidden", false);

            let formData = new FormData();

            formData.append('wav_blob', wav_blob);
            formData.append('file_type', "record_file");
            formData.append('operation_type', voiceprint_record_btn.attr('data-type'));  // 因ajax使用同一个处理函数，所以此处作为一个标记区分。
            formData.append('operation', voiceprint_record_btn.attr('data-operation'));
            formData.append('userid', userid.val())

            $.ajax({
                type: 'POST',
                url: '/ajax/voiceprint',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if (data.msg === 'success') {
                        voice_content.val(data.msg);  // 在文本框内填充返回的文本(此处后期修改对象)
                        recorder.destroy().then(function () {
                            recorder = null;  // 销毁录音实例，置为null释放资源，fn为回调函数，
                        });
                    } else {
                        voice_content.val(data.msg);
                    }
                },
            });

            voiceprint_recording_click_sign = 0; // 重复点击标识复位
            time = null; // 重置最低语音限制标识time
        });
    })
});
