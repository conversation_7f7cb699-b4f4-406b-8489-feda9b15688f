$(function () {
    var recording_click_sign = 0; //0代表recording_button未被点击，1代表已被点击，每次recording-button点击事件完毕重置
    var recorder

    $('#record-button').on('click', function () {
        if (recorder == null) { // 如果recorder实例被清除，那么重新创建实例对象。
            recorder = Recorder({
                type: "wav",
                bitRate: 16, // 采样位数，支持 8 或 16，默认是16
                sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000
                onProcess: function (buffers, powerLevel, bufferDuration, bufferSampleRate) {}
            });
        }

        var t = setTimeout(function () {
            console.log("无法录音：权限请求被忽略!");
        }, 8000);

        recorder.open(function () { //打开麦克风授权获得相关资源
            clearTimeout(t);
            recorder.start(); //开始录音
            $('#recording-button').attr("hidden", false);
            $('#record-button').attr("hidden", true);

        }, function (msg, isUserNotAllow) {
            clearTimeout(t);
            console.log((isUserNotAllow ? "UserNotAllow，" : "") + "无法录音:" + msg);
        }); // 暂时设置为大延时(才能在手动停止录音后，残留的定时事件不影响之后的录音)
    });


    $('#recording-button').on('click', function () {
        if (recording_click_sign === 1) { //如果当前事件未结束时 又被触发点击事件 则退出事件
            return toast(['请不要重复点击停止按钮'], 1500);
        }

        recording_click_sign = 1 // (全局参数)第一次开始本事件后，置为1，表示事件已被触发，再次点击将不再触发。(事件结尾重置)

        recorder.stop(function (blob, duration) {
            $('#recording-button').attr("hidden", true);
            $('#record-button').attr("hidden", false);
            let formData = new FormData();

            formData.append('wav_blob', blob); //  这是blob数据
            formData.append('file_type', "record_file"); // 实时录音解析 or 上传文件解析 的标识
            formData.append('type', $('#record-button').attr('data-type')); // 因 不同识别类型按钮 使用同一个处理函数，所以此处作为一个标记区分。

            $('#voice-content').val('识别中...'); // 在文本框内填充返回的文本

            $.ajax({
                type: 'POST',
                url: '/ajax/asr',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if (data.state === 'Success') {
                        $('#voice-content').val(data.msg);
                    } else {
                        toast([data.msg], 1500); // toast弹窗显示错误信息
                    }
                },
                error: function () {
                    toast(['请求异常, 请重试.'], 1500)
                    $('#voice-content').val("");
                }
            })
        })
        // 销毁录音实例
        recorder.close();
        // 重复点击标识复位
        recording_click_sign = 0;
    })

    $('#submit-file').on('click', function () {
        // 获取表单对象
        let fm = $('#upload-form');
        let formData = new FormData(fm[0]);

        // 根据当前选择的识别类型确定使用哪个接口
        let recordType = $('#record-button').attr('data-type');

        $('#voice-content').val("识别中...");

        if (recordType === "sex-asr") {
            // 性别识别使用原有的 /ajax/asr 接口
            formData.append("file_type", "upload_file");
            formData.append("type", recordType);

            $.ajax({
                type: 'POST',
                url: '/ajax/asr',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if (data.state == 'Success') {
                        $('#voice-content').val(data.msg);
                    } else {
                        toast([data.msg], 1500);
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Ajax error:', xhr.responseText);
                    toast(['请求异常, 请重试.'], 1500);
                    $('#voice-content').val("");
                }
            });
        } else {
            // 语音识别（普通话和闽南语）使用 /ajax/wechat/dotcasr_proxy 接口
            let asrType = "putonghua"; // 默认普通话
            if (recordType === "mn-asr") {
                asrType = "minnanyu";
            }
            formData.append("asr_type", asrType);

            $.ajax({
                type: 'POST',
                url: '/ajax/wechat/dotcasr_proxy',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function (data) {
                    if (data.state == 'Success') {
                        $('#voice-content').val(data.msg);
                    } else {
                        toast([data.msg], 1500);
                    }
                },
                error: function (xhr, status, error) {
                    console.log('Ajax error:', xhr.responseText);
                    toast(['请求异常, 请重试.'], 1500);
                    $('#voice-content').val("");
                }
            });
        }
    });

    $('#asr').on('click', function () {
        $('#record-button').attr('data-type', "asr");
    });
    $('#mn-asr').on('click', function () {
        $('#record-button').attr('data-type', "mn-asr");
    });
    $('#sex-asr').on('click', function () {
        $('#record-button').attr('data-type', "sex-asr");
    });
})